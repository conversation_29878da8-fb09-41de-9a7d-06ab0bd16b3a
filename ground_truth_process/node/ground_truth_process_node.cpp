#include "ground_truth_process_module.h"
#include "ament_index_cpp/get_package_prefix.hpp"
#include "ament_index_cpp/get_package_share_directory.hpp"
#include "uenv/env.h"
#include "uparam/uparam.h"

using namespace uslam::module;

int main(int argc, char *argv[])
{
    uauto::env::init(argc, argv);

    std::string config_path_folder = "";
    try {
        // 首先尝试获取当前包的配置路径
        config_path_folder = ament_index_cpp::get_package_share_directory("ground_truth_process_task");
        ULOGI("Found ground_truth_process_task package config path: %s", config_path_folder.c_str());
    } catch (const ament_index_cpp::PackageNotFoundError &e) {
        ULOGE("Cannot find ground_truth_process_task package");
        
        // 如果找不到当前包，尝试使用环境变量中的配置包
        if (!uauto::env::getConfigPackage().empty()) {
            config_path_folder = uauto::env::getConfigPackage().string();
            ULOGI("Using config package from environment: %s", config_path_folder.c_str());
        } else {
            // 最后的回退方案：使用默认路径
            config_path_folder = "/home/<USER>/workspace/uautopilot_simulation/ground_truth_process/config";
            ULOGI("Using hardcoded default config path: %s", config_path_folder.c_str());
        }
    }

    ULOGI("Final config path folder: %s", config_path_folder.c_str());

    // run module
    uslam::module::GroundTruthProcessModule module(uauto::env::getModuleName(), config_path_folder);

    auto ret = module.init();
    if (!ret) {
        ULOGI("%s init failed", uauto::env::getModuleName().c_str());
        return -1;
    }
    module.spin();
    return 0;
}