#ifndef GROUND_TRUTH_PROCESS_MODULE_H
#define GROUND_TRUTH_PROCESS_MODULE_H

#include <string>
#include "umodule/module_base.h"
#include "utransport/reader.h"
#include "nav_msgs/msg/Odometry.h"
#include "sensor_msgs/msg/PointCloud2.h"
#include "geometry_msgs/msg/PoseWithCovarianceStamped.h"
#include "geometry_msgs/msg/PoseStamped.h"
#include "std_msgs/msg/Float64MultiArray.h"
#include "pcl/point_cloud.h"
#include <pcl/io/pcd_io.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/point_types.h>
#include <mutex>
#include <message_filters/cache.h>
#include <fstream>
#include <string>
#include <vector>
#include <memory>
#include "yaml-cpp/yaml.h"

namespace velodyne_ros {
struct EIGEN_ALIGN16 Point {
    PCL_ADD_POINT4D;
    float intensity;
    double time;
    uint16_t ring;
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW
};
}  // namespace velodyne_ros

POINT_CLOUD_REGISTER_POINT_STRUCT(velodyne_ros::Point,
                                  (float, x, x)(float, y, y)(float, z, z)(float, intensity,
                                                                          intensity)(double, time, time)(std::uint16_t,
                                                                                                         ring, ring))
namespace uslam {
namespace module {

class GroundTruthProcessModule : public ModuleBase
{
public:
    GroundTruthProcessModule(const std::string &name, const std::string &config_file);
    ~GroundTruthProcessModule();
    bool onInit() override;
    // template <typename BufferT, typename TimeExtractor>
    // std::pair<int, double> find_nearest_index(const BufferT &buffer, double target_time, TimeExtractor get_time)
    // {
    //     double min_diff = std::numeric_limits<double>::max();
    //     int min_idx = -1;
        
    //     // for (size_t i = 0; i < buffer->getAllElem().size(); ++i) {
    //     //     double diff = std::abs(get_time((*buffer)[i]) - target_time);
    //     //     if (diff < min_diff) {
    //     //         min_diff = diff;
    //     //         min_idx = static_cast<int>(i);
    //     //     }
    //     // }
    //     return {min_idx, min_diff};
    // }

private:
    int N_SCANS = 16;
    pcl::PointCloud<pcl::PointXYZINormal> cloud_adapt;
    pcl::PointCloud<pcl::PointXYZINormal> scan_buf[100];
    std::shared_ptr<uslam::transport::Writer<std_msgs::msg::Float64MultiArray>> pose_diff_debugger;
    // Buffers
    std::shared_ptr<message_filters::Cache<nav_msgs::msg::Odometry>> lidar_cache_;              // lidar ground truth for pcd saving
    std::shared_ptr<message_filters::Cache<geometry_msgs::msg::PoseStamped>> base_pose_cache_;  // base ground truth for comparison
    std::shared_ptr<message_filters::Cache<nav_msgs::msg::Odometry>> localization_odom_cache_;  // localization pose for comparison
    nav_msgs::msg::Odometry last_saved_odom_;
    bool has_last_saved_odom_ = false;
    geometry_msgs::msg::PoseStamped last_base_pose_;
    bool has_last_base_pose_ = false;
    std::mutex lidar_mutex_;
    std::mutex base_mutex_;
    std::mutex localization_odom_mutex_;
    std::string config_path_;

    // File output
    std::string save_dir_;
    std::string pcd_dir_;
    std::ofstream lidar_gt_f_;
    std::ofstream debug_f_;
    std::ofstream base_gt_f_;
    size_t pcd_save_idx_ = 0;
    size_t base_pose_idx_ = 0;
    double time_offset_ = 0.0;

    // Configuration parameters
    bool enable_lidar_ground_truth_save_ = true;
    bool enable_base_ground_truth_save_ = true;
    double min_translation_distance_ = 3.0;
    double min_rotation_angle_ = 10.0;
    size_t lidar_buffer_size_ = 200;
    size_t base_buffer_size_ = 200;
    size_t localization_buffer_size_ = 200;
    double max_time_diff_ = 0.1;
    std::string localization_pose_topic_ = "/localization_pose_base";
    std::string odom_difference_topic_ = "/odom/difference";
    std::string lidar_gt_topic_ = "/lidar_gt";
    std::string base_gt_topic_ = "/base_gt";
    std::string pcd_topic_ = "/pcd";
    bool latest_msg_enable_ = false;

    void pcd_preprocess(const std::shared_ptr<sensor_msgs::msg::PointCloud2> &msg);
    void lidar_gt_callback(const std::shared_ptr<nav_msgs::msg::Odometry> &msg);
    void base_gt_callback(const std::shared_ptr<geometry_msgs::msg::PoseStamped> &msg);
    void localization_odom_callback(const std::shared_ptr<nav_msgs::msg::Odometry> &msg);
    void pcd_callback(const std::shared_ptr<sensor_msgs::msg::PointCloud2> &msg);
    bool is_odom_moving(const nav_msgs::msg::Odometry &o1, const nav_msgs::msg::Odometry &o2);
    bool is_base_moving(const geometry_msgs::msg::PoseStamped &p1, const geometry_msgs::msg::PoseStamped &p2);
    nav_msgs::msg::Odometry interpolate_odom(double target_time, bool &success, std::string &debug_line);
    geometry_msgs::msg::PoseStamped interpolate_base_pose(double target_time, bool &success);
    std_msgs::msg::Float64MultiArray calculate_odom_difference(const std::shared_ptr<const nav_msgs::msg::Odometry> &loc_pose,
                                                               bool &is_match);
    std_msgs::msg::Float64MultiArray calculate_base_difference(
        const std::shared_ptr<const geometry_msgs::msg::PoseStamped> &base_pose, bool &is_match);
    geometry_msgs::msg::Point lerp_position(const geometry_msgs::msg::Point &p0, const geometry_msgs::msg::Point &p1,
                                            double alpha);
    geometry_msgs::msg::Quaternion improved_slerp_quaternion(const geometry_msgs::msg::Quaternion &q0,
                                                             const geometry_msgs::msg::Quaternion &q1, double t);
    double translation_distance(const geometry_msgs::msg::Point &p1, const geometry_msgs::msg::Point &p2);
    double rotation_angle(const geometry_msgs::msg::Quaternion &q1, const geometry_msgs::msg::Quaternion &q2);
    bool loadConfig(const std::string &config_path);
    nav_msgs::msg::Odometry find_nearest_odom(double target_time, bool &success);
};
}  // namespace module
}  // namespace uslam

#endif  // GROUND_TRUTH_PROCESS_MODULE_H