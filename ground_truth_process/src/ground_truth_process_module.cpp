#include "ground_truth_process_module.h"
#include <filesystem>
#include <chrono>
#include <cmath>
#include <Eigen/Dense>
#include <ulog/ulog.h>
#include "uenv/env.h"
#include "yaml-cpp/yaml.h"

using namespace uslam;
using namespace uslam::module;
using namespace std;
using namespace Eigen;

GroundTruthProcessModule::GroundTruthProcessModule(const std::string &name, const std::string &config_path) :
    ModuleBase(name), config_path_(config_path)
{
    ULOGI("GroundTruthProcessModule started.");
}

GroundTruthProcessModule::~GroundTruthProcessModule()
{
    if (lidar_gt_f_.is_open()) lidar_gt_f_.close();
    if (base_gt_f_.is_open()) base_gt_f_.close();
    if (debug_f_.is_open()) debug_f_.close();

    {
        std::lock_guard<std::mutex> lock(lidar_mutex_);
        if (lidar_cache_) {
            lidar_cache_.reset();
        }
    }
    {
        std::lock_guard<std::mutex> lock(base_mutex_);
        if (base_pose_cache_) {
            base_pose_cache_.reset();
        }
    }
    {
        std::lock_guard<std::mutex> lock(localization_odom_mutex_);
        if (localization_odom_cache_) {
            localization_odom_cache_.reset();
        }
    }

    ULOGI("%s:%d", __FUNCTION__, __LINE__);
}

bool GroundTruthProcessModule::onInit()
{
    // 读取配置文件
    if (!loadConfig(config_path_)) {
        ULOGE("Failed to load configuration, using default values");
    }

    // std::string ipm_configfile = config_path_ + "/config/locate3d/ipm_process_config.yaml";
    // if (!std::filesystem::exists(ipm_configfile)) {
    //     ULOGE("Config file %s not exist", ipm_configfile.c_str());
    //     // return false;
    // }

    // 使用配置文件中的保存路径，如果没有配置则使用默认路径
    if (save_dir_.empty()) {
        save_dir_ = "/home/<USER>/workspace/uautopilot_simulation/data_save";
    }
    pcd_dir_ = save_dir_ + "/pcd";
    std::filesystem::create_directories(pcd_dir_);
    lidar_gt_f_.open(save_dir_ + "/odom.txt");
    base_gt_f_.open(save_dir_ + "/base.txt");
    // debug_f_.open(save_dir_ + "/debug.txt");
    // debug_f_ << "timestamp,pcd_time,odom_before_time,odom_after_time,alpha,interp_quat_w,interp_quat_x,interp_quat_y,"
    //             "interp_quat_z,odom_moving\n";

    uslam::transport::TransportAttribute lidar_gt_attr(lidar_gt_topic_,
                                                       uslam::transport::DDSQosProfile::QOS_PROFILE_PUB_DEFAULT());
    createReader<nav_msgs::msg::Odometry>(
        lidar_gt_attr, std::bind(&GroundTruthProcessModule::lidar_gt_callback, this, std::placeholders::_1));

    uslam::transport::TransportAttribute base_gt_attr(base_gt_topic_,
                                                      uslam::transport::DDSQosProfile::QOS_PROFILE_PUB_DEFAULT());
    createReader<geometry_msgs::msg::PoseStamped>(
        base_gt_attr, std::bind(&GroundTruthProcessModule::base_gt_callback, this, std::placeholders::_1));

    uslam::transport::TransportAttribute localization_odom_attr(
        localization_pose_topic_, uslam::transport::DDSQosProfile::QOS_PROFILE_PUB_DEFAULT());
    createReader<nav_msgs::msg::Odometry>(
        localization_odom_attr,
        std::bind(&GroundTruthProcessModule::localization_odom_callback, this, std::placeholders::_1));

    uslam::transport::TransportAttribute odom_diff_attr(odom_difference_topic_,
                                                        uslam::transport::DDSQosProfile::QOS_PROFILE_PUB_DEFAULT());
    pose_diff_debugger = createWriter<std_msgs::msg::Float64MultiArray>(odom_diff_attr);

    uslam::transport::TransportAttribute pcd_attr(pcd_topic_,
                                                  uslam::transport::DDSQosProfile::QOS_PROFILE_PUB_DEFAULT());
    createReader<sensor_msgs::msg::PointCloud2>(
        pcd_attr, std::bind(&GroundTruthProcessModule::pcd_callback, this, std::placeholders::_1));

    lidar_cache_ = std::make_shared<message_filters::Cache<nav_msgs::msg::Odometry>>(lidar_buffer_size_);
    base_pose_cache_ = std::make_shared<message_filters::Cache<geometry_msgs::msg::PoseStamped>>(base_buffer_size_);
    localization_odom_cache_ = std::make_shared<message_filters::Cache<nav_msgs::msg::Odometry>>(localization_buffer_size_);

    ULOGI("GroundTruthProcessModule Init Succeeded ");
    return true;
}

void GroundTruthProcessModule::lidar_gt_callback(const std::shared_ptr<nav_msgs::msg::Odometry> &msg)
{
    double lidar_time = msg->header.stamp.sec + msg->header.stamp.nanosec * 1e-9;
    std::lock_guard<std::mutex> lock(lidar_mutex_);
    lidar_cache_->add(std::make_shared<const nav_msgs::msg::Odometry>(*msg), msg->header.stamp);
    // ULOGI("Lidar ground truth time: %.9f", msg->header.stamp.sec + msg->header.stamp.nanosec * 1e-9);
}

void GroundTruthProcessModule::base_gt_callback(const std::shared_ptr<geometry_msgs::msg::PoseStamped> &msg)
{
    if (enable_base_ground_truth_save_) {
        double base_odom_time = msg->header.stamp.sec + msg->header.stamp.nanosec * 1e-9;
        if (has_last_base_pose_) {
            double trans = translation_distance(msg->pose.position, last_base_pose_.pose.position);
            double rot = rotation_angle(msg->pose.orientation, last_base_pose_.pose.orientation);

            if (trans < min_translation_distance_ && rot < min_rotation_angle_) {
                return;
            }
        }

        const auto &pos = msg->pose.position;
        const auto &ori = msg->pose.orientation;

        // 写入文件
        base_gt_f_ << base_odom_time << "," << pos.x << "," << pos.y << "," << pos.z << "," << ori.x << "," << ori.y
                   << "," << ori.z << "," << ori.w << "\n";

        if (base_pose_idx_ % 5 == 0) {
            base_gt_f_.flush();
        }

        last_base_pose_ = *msg;
        has_last_base_pose_ = true;
        base_pose_idx_++;
    }
    double base_time = msg->header.stamp.sec + msg->header.stamp.nanosec * 1e-9;
    // ULOGI("Base ground truth time: %.9f", base_time);
    // ULOGI("Base ground truth time 01: %.9f", msg->header.stamp.sec + msg->header.stamp.nanosec * 1e-9);
    {
        std::lock_guard<std::mutex> lock(base_mutex_);
        base_pose_cache_->add(std::make_shared<const geometry_msgs::msg::PoseStamped>(*msg), msg->header.stamp);
    }

    // Try to calculate differences for recent localization poses
    if (latest_msg_enable_) {
        bool is_match = false;
        std_msgs::msg::Float64MultiArray diff_msg = calculate_base_difference(msg, is_match);
        if (is_match) {
            pose_diff_debugger->Write(diff_msg);
        }
    }
    bool is_match = false;
    std_msgs::msg::Float64MultiArray diff_msg = calculate_base_difference(msg, is_match);
    if (is_match) {
        pose_diff_debugger->Write(diff_msg);
    }
}

void GroundTruthProcessModule::localization_odom_callback(const std::shared_ptr<nav_msgs::msg::Odometry> &msg)
{
    double locate_time = msg->header.stamp.sec + msg->header.stamp.nanosec * 1e-9;
    ULOGI("localization odom time: %.9f", locate_time);
    ULOGI("localization odom time 01: %.9f", msg->header.stamp.sec + msg->header.stamp.nanosec * 1e-9);

    {
        std::lock_guard<std::mutex> lock(localization_odom_mutex_);
        localization_odom_cache_->add(std::make_shared<const nav_msgs::msg::Odometry>(*msg), msg->header.stamp);
    }
}

bool GroundTruthProcessModule::is_odom_moving(const nav_msgs::msg::Odometry &o1, const nav_msgs::msg::Odometry &o2)
{
    const auto &pos1 = o1.pose.pose.position;
    const auto &pos2 = o2.pose.pose.position;
    double pos_diff =
        std::sqrt(std::pow(pos1.x - pos2.x, 2) + std::pow(pos1.y - pos2.y, 2) + std::pow(pos1.z - pos2.z, 2));
    const auto &q1 = o1.pose.pose.orientation;
    const auto &q2 = o2.pose.pose.orientation;
    double dot = std::abs(q1.w * q2.w + q1.x * q2.x + q1.y * q2.y + q1.z * q2.z);
    double angle_diff = 2 * std::acos(std::min(dot, 1.0)) * 180 / M_PI;
    return pos_diff > 0.001 || angle_diff > 0.1;
}

bool GroundTruthProcessModule::is_base_moving(const geometry_msgs::msg::PoseStamped &p1,
                                              const geometry_msgs::msg::PoseStamped &p2)
{
    const auto &pos1 = p1.pose.position;
    const auto &pos2 = p2.pose.position;
    double pos_diff =
        std::sqrt(std::pow(pos1.x - pos2.x, 2) + std::pow(pos1.y - pos2.y, 2) + std::pow(pos1.z - pos2.z, 2));
    const auto &q1 = p1.pose.orientation;
    const auto &q2 = p2.pose.orientation;
    double dot = std::abs(q1.w * q2.w + q1.x * q2.x + q1.y * q2.y + q1.z * q2.z);
    double angle_diff = 2 * std::acos(std::min(dot, 1.0)) * 180 / M_PI;
    return pos_diff > 0.001 || angle_diff > 0.1;
}

geometry_msgs::msg::Point GroundTruthProcessModule::lerp_position(const geometry_msgs::msg::Point &p0,
                                                                  const geometry_msgs::msg::Point &p1, double alpha)
{
    geometry_msgs::msg::Point pos;
    pos.x = (1 - alpha) * p0.x + alpha * p1.x;
    pos.y = (1 - alpha) * p0.y + alpha * p1.y;
    pos.z = (1 - alpha) * p0.z + alpha * p1.z;
    return pos;
}

geometry_msgs::msg::Quaternion GroundTruthProcessModule::improved_slerp_quaternion(
    const geometry_msgs::msg::Quaternion &q0, const geometry_msgs::msg::Quaternion &q1, double t)
{
    Eigen::Vector4d v0(q0.w, q0.x, q0.y, q0.z);
    Eigen::Vector4d v1(q1.w, q1.x, q1.y, q1.z);
    v0.normalize();
    v1.normalize();
    double dot = v0.dot(v1);
    if (dot < 0.0) {
        v0 = -v0;
        dot = -dot;
    }
    const double DOT_THRESHOLD = 0.9995;
    Eigen::Vector4d result;
    if (dot > DOT_THRESHOLD) {
        result = v0 + t * (v1 - v0);
        result.normalize();
    } else {
        double theta_0 = std::acos(std::clamp(dot, -1.0, 1.0));
        double sin_theta_0 = std::sin(theta_0);
        if (sin_theta_0 < 1e-6) {
            result = v0;
        } else {
            double theta = theta_0 * t;
            double sin_theta = std::sin(theta);
            double s0 = std::sin((1.0 - t) * theta_0) / sin_theta_0;
            double s1 = sin_theta / sin_theta_0;
            result = s0 * v0 + s1 * v1;
        }
        result.normalize();
    }
    geometry_msgs::msg::Quaternion quat;
    quat.w = result[0];
    quat.x = result[1];
    quat.y = result[2];
    quat.z = result[3];
    return quat;
}

double GroundTruthProcessModule::translation_distance(const geometry_msgs::msg::Point &p1,
                                                      const geometry_msgs::msg::Point &p2)
{
    return std::sqrt((p1.x - p2.x) * (p1.x - p2.x) + (p1.y - p2.y) * (p1.y - p2.y) + (p1.z - p2.z) * (p1.z - p2.z));
}

double GroundTruthProcessModule::rotation_angle(const geometry_msgs::msg::Quaternion &q1,
                                                const geometry_msgs::msg::Quaternion &q2)
{
    double dot = std::abs(q1.x * q2.x + q1.y * q2.y + q1.z * q2.z + q1.w * q2.w);
    dot = std::min(dot, 1.0);
    double angle_rad = 2 * std::acos(dot);
    return angle_rad * 180.0 / M_PI;
}

void GroundTruthProcessModule::pcd_callback(const std::shared_ptr<sensor_msgs::msg::PointCloud2> &msg)
{

    // 检查是否启用雷达点云真值pose保存功能
    if (!enable_lidar_ground_truth_save_) {
        return;
    }
    cloud_adapt.clear();  // 每次处理前清空
    pcd_preprocess(msg);
    float max_time = std::numeric_limits<float>::lowest();
    for (const auto &pt : cloud_adapt.points) {
        // UINFO_EVERY(20) << "point time :" << pt.curvature;
        if (pt.curvature > max_time) {
            max_time = pt.curvature;
        }
    }
    //时间补偿
    // double pcd_time = msg->header.stamp.sec + msg->header.stamp.nanosec * 1e-9 + max_time;
    double pcd_time = msg->header.stamp.sec + msg->header.stamp.nanosec * 1e-9 + 0.1;
    // ULOGI("max_time: %f, ori header time :%f", max_time, msg->header.stamp.sec + msg->header.stamp.nanosec * 1e-9);

    bool success = false;
    std::string debug_line;
    // 查找lidar数据中时间戳最接近的元素（用于pcd保存）
    nav_msgs::msg::Odometry current_odom = find_nearest_odom(pcd_time, success);
    if (!success) {
        ULOGW("No lidar odometry for point cloud time, skip.");
        return;
    }

    double trans = 0.0, rot = 0.0;
    if (has_last_saved_odom_) {
        trans = translation_distance(current_odom.pose.pose.position, last_saved_odom_.pose.pose.position);
        rot = rotation_angle(current_odom.pose.pose.orientation, last_saved_odom_.pose.pose.orientation);
        if (trans < min_translation_distance_ && rot < min_rotation_angle_) return;
    }
    std::string filename_prefix = std::to_string(pcd_save_idx_);
    // if (cloud_points.empty()) {
    //     ULOGW("Point cloud empty, skip.");
    //     return;
    // }
    std::string pcd_path = pcd_dir_ + "/" + filename_prefix + ".pcd";
    pcl::io::savePCDFileBinary(pcd_path, cloud_adapt);
    const auto &pos = current_odom.pose.pose.position;
    const auto &ori = current_odom.pose.pose.orientation;
    double odom_time = current_odom.header.stamp.sec + current_odom.header.stamp.nanosec * 1e-9;
    lidar_gt_f_ << odom_time << "," << pos.x << "," << pos.y << "," << pos.z << "," << ori.x << "," << ori.y << "," << ori.z
            << "," << ori.w << "\n";
    lidar_gt_f_.flush();
    // debug_f_ << debug_line;
    // debug_f_.flush();
    last_saved_odom_ = current_odom;
    has_last_saved_odom_ = true;
    ULOGI("Saved: %s.pcd, trans: %.2f m, rot: %.2f deg", filename_prefix.c_str(), trans, rot);
    pcd_save_idx_++;
}

void GroundTruthProcessModule::pcd_preprocess(const std::shared_ptr<sensor_msgs::msg::PointCloud2> &msg)
{
    pcl::PointCloud<velodyne_ros::Point> pcd_orig;
    pcl::fromROSMsg(*msg, pcd_orig);
    int cloud_size = pcd_orig.points.size();
    if (cloud_size == 0) {
        ULOGE("cloud is empty !");
        return;
    }
    // if (feature_enabled) {
    {
        for (int i = 0; i < N_SCANS; i++) {
            scan_buf[i].clear();
            scan_buf[i].reserve(cloud_size);
        }

        for (int i = 0; i < cloud_size; i++) {
            pcl::PointXYZINormal added_pt;
            added_pt.normal_x = 0;
            added_pt.normal_y = 0;
            added_pt.normal_z = 0;
            int layer = pcd_orig.points[i].ring;
            if (layer >= N_SCANS) continue;
            added_pt.x = pcd_orig.points[i].x;
            added_pt.y = pcd_orig.points[i].y;
            added_pt.z = pcd_orig.points[i].z;
            added_pt.intensity = pcd_orig.points[i].intensity;
            added_pt.curvature = pcd_orig.points[i].time;
            added_pt.normal_x = layer;
            added_pt.normal_y = scan_buf[layer].points.size();

            cloud_adapt.points.push_back(added_pt);
            scan_buf[layer].points.push_back(added_pt);
        }
    }
}

std_msgs::msg::Float64MultiArray GroundTruthProcessModule::calculate_odom_difference(
    const std::shared_ptr<const nav_msgs::msg::Odometry> &loc_pose, bool &is_match)
{
    std_msgs::msg::Float64MultiArray diff_msg;
    is_match = false;

    geometry_msgs::msg::PoseStamped base_pose;
    // nav_msgs::msg::Odometry loc_pose;
    bool base_success = false, loc_success = false;
    bool moving = false;
    // double target_time = unav::Time(loc_pose->header.stamp).toSec();
    // 补偿时间
    double target_time = loc_pose->header.stamp.sec + loc_pose->header.stamp.nanosec * 1e-9;
    ULOGI("Target time: %.9f", target_time);
    ULOGI("Lidar ground truth time: %.9f", loc_pose->header.stamp.sec + loc_pose->header.stamp.nanosec * 1e-9);

    // 查找 base pose
    {
        std::lock_guard<std::mutex> base_lock(base_mutex_);
        if (!base_pose_cache_ || base_pose_cache_->getAllElem().size() < 2) {
            ULOGW("Base pose buffer size : %d", base_pose_cache_->getAllElem().size());
            return diff_msg;
        }

        auto before_msg = base_pose_cache_->getElemBeforeTime(unav::Time(target_time));
        auto after_msg = base_pose_cache_->getElemAfterTime(unav::Time(target_time));
        if (!before_msg || !after_msg) {
            return diff_msg;
        }
        auto msg_interval = base_pose_cache_->getInterval(before_msg->header.stamp, after_msg->header.stamp);
        ULOGI("Base pose interval size: %d", msg_interval.size());

        if (latest_msg_enable_) {
            // 判断是否移动
            if (before_msg && after_msg) {
                moving = is_base_moving(*before_msg, *after_msg);
            }

            if (msg_interval.size() > 1) {
                base_pose = *msg_interval[1];  // Directly use the second message in the interval
                double msg_time = base_pose.header.stamp.sec + base_pose.header.stamp.nanosec * 1e-9;
                ULOGI("Base pose time: %.9f, target time: %.9f", msg_time, target_time);
                if (std::abs(msg_time - target_time) > max_time_diff_) {
                    ULOGW("Base pose time difference too large: %.9f", std::abs(msg_time - target_time));
                    return diff_msg;
                }
                base_success = true;
            } else {
                ULOGW("Insufficient messages in interval");
                return diff_msg;
            }
        } else {
            base_pose = *(base_pose_cache_->getLatestElem());
            ULOGI("Base pose time: %.9f, target time: %.9f",
                  base_pose.header.stamp.sec + base_pose.header.stamp.nanosec * 1e-9, target_time);
        }
    }

    if (!base_success) ULOGW("No base pose data for t=%.6f", target_time);

    // 计算差值
    const auto &bp = base_pose.pose.position;
    const auto &lp = loc_pose->pose.pose.position;
    const auto &bo = base_pose.pose.orientation;
    const auto &lo = loc_pose->pose.pose.orientation;

    double pos_diff_x = bp.x - lp.x;  // base - localization
    double pos_diff_y = bp.y - lp.y;
    double pos_diff_z = bp.z - lp.z;
    double pos_distance = std::hypot(pos_diff_x, pos_diff_y);  // 使用已计算的差值
    double rot_angle = rotation_angle(bo, lo);

    diff_msg.data = {base_pose.header.stamp.sec + base_pose.header.stamp.nanosec * 1e-9,
                     loc_pose->header.stamp.sec + loc_pose->header.stamp.nanosec * 1e-9,
                     0.0,  // 你可以算时间差填这里
                     pos_diff_x,
                     pos_diff_y,
                     pos_diff_z,
                     pos_distance,
                     0.0,
                     0.0,
                     0.0,
                     rot_angle,
                     moving ? 1.0 : 0.0,
                     1.0};

    is_match = true;
    return diff_msg;
}

std_msgs::msg::Float64MultiArray GroundTruthProcessModule::calculate_base_difference(const std::shared_ptr<const geometry_msgs::msg::PoseStamped> &base_pose,
                                                           bool &is_match)
{
    std_msgs::msg::Float64MultiArray diff_msg;
    is_match = false;

    nav_msgs::msg::Odometry loc_pose;
    bool loc_success = false;
    bool moving = false;
    // double target_time = unav::Time(loc_pose->header.stamp).toSec();
    // 补偿时间
    double target_time = base_pose->header.stamp.sec + base_pose->header.stamp.nanosec * 1e-9 - 0.05;
    ULOGI("Target time: %.9f", target_time);
    ULOGI("Lidar ground truth time: %.9f", base_pose->header.stamp.sec + base_pose->header.stamp.nanosec * 1e-9);

    // 查找 localization pose
    {
        std::lock_guard<std::mutex> loc_lock(localization_odom_mutex_);
        if (!localization_odom_cache_ || localization_odom_cache_->getAllElem().size() < 2) {
            ULOGW("Localization buffer size : %d", localization_odom_cache_->getAllElem().size());
            return diff_msg;
        }

        auto before_msg = localization_odom_cache_->getElemBeforeTime(unav::Time(target_time));
        auto after_msg = localization_odom_cache_->getElemAfterTime(unav::Time(target_time));
        if (!before_msg || !after_msg) {
            return diff_msg;
        }
        auto msg_interval = localization_odom_cache_->getInterval(before_msg->header.stamp, after_msg->header.stamp);
        ULOGI("Localization pose interval size: %d", msg_interval.size());

        if (latest_msg_enable_) {
            // 判断是否移动
            if (before_msg && after_msg) {
                moving = is_odom_moving(*before_msg, *after_msg);
            }

            if (msg_interval.size() > 1) {
                loc_pose = *msg_interval[1];  // Directly use the second message in the interval
                double msg_time = loc_pose.header.stamp.sec + loc_pose.header.stamp.nanosec * 1e-9;
                ULOGI("Localization pose time: %.9f, target time: %.9f", msg_time, target_time);
                if (std::abs(msg_time - target_time) > max_time_diff_) {
                    ULOGW("Localization pose time difference too large: %.9f", std::abs(msg_time - target_time));
                    return diff_msg;
                }
                loc_success = true;
            } else {
                ULOGW("Insufficient messages in interval");
                return diff_msg;
            }
        } else {
            loc_pose = *(localization_odom_cache_->getLatestElem());
            ULOGI("Localization pose time: %.9f, target time: %.9f",
                  loc_pose.header.stamp.sec + loc_pose.header.stamp.nanosec * 1e-9, target_time);
        }
    }

    if (!loc_success) ULOGW("No localization pose data for t=%.6f", target_time);

    // 计算差值
    const auto &bp = base_pose->pose.position;
    const auto &lp = loc_pose.pose.pose.position;
    const auto &bo = base_pose->pose.orientation;
    const auto &lo = loc_pose.pose.pose.orientation;

    double pos_diff_x = bp.x - lp.x;  // base - localization
    double pos_diff_y = bp.y - lp.y;
    double pos_diff_z = bp.z - lp.z;
    double pos_distance = std::hypot(pos_diff_x, pos_diff_y);  // 使用已计算的差值
    double rot_angle = rotation_angle(bo, lo);

    diff_msg.data = {base_pose->header.stamp.sec + base_pose->header.stamp.nanosec * 1e-9,
                     loc_pose.header.stamp.sec + loc_pose.header.stamp.nanosec * 1e-9,
                     0.0,  // 你可以算时间差填这里
                     pos_diff_x,
                     pos_diff_y,
                     pos_diff_z,
                     pos_distance,
                     0.0,
                     0.0,
                     0.0,
                     rot_angle,
                     moving ? 1.0 : 0.0,
                     1.0};

    is_match = true;
    return diff_msg;
}

bool GroundTruthProcessModule::loadConfig(const std::string &config_path)
{
    std::string config_file = config_path + "/config/ground_truth_process.yaml";
    if (!std::filesystem::exists(config_file)) {
        ULOGW("Config file %s not found, using default values", config_file.c_str());
        return false;
    }

    try {
        YAML::Node config = YAML::LoadFile(config_file);

        if (config["enable_lidar_ground_truth_save"]) {
            enable_lidar_ground_truth_save_ = config["enable_lidar_ground_truth_save"].as<bool>();
        }
        if (config["enable_base_ground_truth_save"]) {
            enable_base_ground_truth_save_ = config["enable_base_ground_truth_save"].as<bool>();
        }
        if (config["save_dir"]) {
            save_dir_ = config["save_dir"].as<std::string>();
        }
        if (config["min_translation_distance"]) {
            min_translation_distance_ = config["min_translation_distance"].as<double>();
        }
        if (config["min_rotation_angle"]) {
            min_rotation_angle_ = config["min_rotation_angle"].as<double>();
        }
        if (config["lidar_buffer_size"]) {
            lidar_buffer_size_ = config["lidar_buffer_size"].as<size_t>();
        }
        if (config["base_buffer_size"]) {
            base_buffer_size_ = config["base_buffer_size"].as<size_t>();
        }
        if (config["localization_buffer_size"]) {
            localization_buffer_size_ = config["localization_buffer_size"].as<size_t>();
        }
        if (config["max_time_diff"]) {
            max_time_diff_ = config["max_time_diff"].as<double>();
        }
        if (config["localization_pose_topic"]) {
            localization_pose_topic_ = config["localization_pose_topic"].as<std::string>();
        }
        if (config["odom_difference_topic"]) {
            odom_difference_topic_ = config["odom_difference_topic"].as<std::string>();
        }
        if (config["lidar_gt_topic"]) {
            lidar_gt_topic_ = config["lidar_gt_topic"].as<std::string>();
        }
        if (config["base_gt_topic"]) {
            base_gt_topic_ = config["base_gt_topic"].as<std::string>();
        }
        if (config["pcd_topic"]) {
            pcd_topic_ = config["pcd_topic"].as<std::string>();
        }
        if (config["time_offset"]) {
            time_offset_ = config["time_offset"].as<double>();
        }
        if (config["latest_msg_enable"]) {
            latest_msg_enable_ = config["latest_msg_enable"].as<bool>();
        }

        ULOGI("Configuration loaded successfully from %s", config_file.c_str());
        ULOGI("enable_lidar_ground_truth_save: %s", enable_lidar_ground_truth_save_ ? "true" : "false");
        ULOGI("enable_base_ground_truth_save: %s", enable_base_ground_truth_save_ ? "true" : "false");
        ULOGI("min_translation_distance: %.2f", min_translation_distance_);
        ULOGI("min_rotation_angle: %.2f", min_rotation_angle_);
        ULOGI("lidar_buffer_size: %zu", lidar_buffer_size_);
        ULOGI("base_buffer_size: %zu", base_buffer_size_);
        ULOGI("localization_buffer_size: %zu", localization_buffer_size_);
        ULOGI("max_time_diff: %.3f", max_time_diff_);
        ULOGI("localization_pose_topic: %s", localization_pose_topic_.c_str());
        ULOGI("odom_difference_topic: %s", odom_difference_topic_.c_str());
        ULOGI("lidar_gt_topic: %s", lidar_gt_topic_.c_str());
        ULOGI("base_gt_topic: %s", base_gt_topic_.c_str());
        ULOGI("pcd_topic: %s", pcd_topic_.c_str());
        ULOGI("latest_msg_enable: %s", latest_msg_enable_ ? "true" : "false");

    } catch (const YAML::Exception &e) {
        ULOGE("Failed to load or parse config file %s: %s", config_file.c_str(), e.what());
        return false;
    }
    return true;
}

nav_msgs::msg::Odometry GroundTruthProcessModule::find_nearest_odom(double target_time, bool &success)
{
    std::lock_guard<std::mutex> lock(lidar_mutex_);
    success = false;
    nav_msgs::msg::Odometry nearest_odom;

    if (lidar_cache_->getAllElem().size() == 0) {
        ULOGW("No lidar data available");
        return nearest_odom;
    }

    unav::Time target_ros_time(target_time);

    // 先找前后两个元素
    auto before_msg = lidar_cache_->getElemBeforeTime(target_ros_time);
    auto after_msg = lidar_cache_->getElemAfterTime(target_ros_time);

    // 如果两边都没有，就直接返回最近的
    if (!before_msg && !after_msg) {
        return nearest_odom;
    }else if(!before_msg) {
        nearest_odom = *after_msg;
        success = true;
        return nearest_odom;
    }else if(!after_msg) {
        nearest_odom = *before_msg;
        success = true;
        return nearest_odom;
    } else {
        // 选最近的一个
        double before_diff =
            before_msg ? std::abs((before_msg->header.stamp.sec + before_msg->header.stamp.nanosec * 1e-9) - target_time)
                       : std::numeric_limits<double>::max();

        double after_diff =
            after_msg ? std::abs((after_msg->header.stamp.sec + after_msg->header.stamp.nanosec * 1e-9) - target_time)
                      : std::numeric_limits<double>::max();

        if (before_diff < after_diff) {
            nearest_odom = *before_msg;
            if (before_diff > max_time_diff_) return nearest_odom;
        } else {
            nearest_odom = *after_msg;
            if (after_diff > max_time_diff_) return nearest_odom;
        }

        // 更新时间戳为目标时间
        // nearest_odom.header.stamp = target_ros_time;
        if (lidar_cache_->getAllElem().size() > 1) {
            // 找 nearest_odom 在缓存中的位置
            auto before_msg = lidar_cache_->getElemBeforeTime(target_ros_time);
            auto after_msg = lidar_cache_->getElemAfterTime(target_ros_time);
        }

        success = true;
        return nearest_odom;
    }
}