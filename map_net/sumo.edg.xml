<?xml version='1.0' encoding='UTF-8'?>
<edges>
  <edge id="1" from="25" to="24" numLanes="2" width="5.375" shape="-105.000000,4.500000 -105.109640,-64.499913 "/>
  <edge id="-1" to="25" from="24" numLanes="2" width="5.375" shape="-105.109640,-64.499913 -105.000000,4.500000 "/>
  <edge id="3" from="23" to="22" numLanes="2" width="5.375" shape="-64.500000,-105.000000 4.500000,-105.000000 "/>
  <edge id="-3" to="23" from="22" numLanes="2" width="5.375" shape="4.500000,-105.000000 -64.500000,-105.000000 "/>
  <edge id="5" from="28" to="16" numLanes="2" width="5.375" shape="45.000000,4.500000 44.999890,-25.500000 "/>
  <edge id="-5" to="28" from="16" numLanes="2" width="5.375" shape="44.999890,-25.500000 45.000000,4.500000 "/>
  <edge id="7" from="17" to="29" numLanes="2" width="5.375" shape="-25.500000,45.000000 4.500000,45.000000 "/>
  <edge id="-7" to="17" from="29" numLanes="2" width="5.375" shape="4.500000,45.000000 -25.500000,45.000000 "/>
  <edge id="8" from="17" to="26" numLanes="2" width="5.375" shape="-45.000000,25.500000 -45.000110,-4.500000 "/>
  <edge id="-8" to="17" from="26" numLanes="2" width="5.375" shape="-45.000110,-4.500000 -45.000000,25.500000 "/>
  <edge id="10" from="27" to="16" numLanes="2" width="5.375" shape="-4.500000,-45.000000 25.500000,-45.000000 "/>
  <edge id="-10" to="27" from="16" numLanes="2" width="5.375" shape="25.500000,-45.000000 -4.500000,-45.000000 "/>
  <edge id="12" from="21" to="20" numLanes="2" width="5.375" shape="105.000000,-4.500000 105.000437,64.500000 "/>
  <edge id="-12" to="21" from="20" numLanes="2" width="5.375" shape="105.000437,64.500000 105.000000,-4.500000 "/>
  <edge id="14" from="19" to="18" numLanes="2" width="5.375" shape="64.500000,105.000000 -4.500000,105.002253 "/>
  <edge id="-14" to="19" from="18" numLanes="2" width="5.375" shape="-4.500000,105.002253 64.500000,105.000000 "/>
  <edge id="0" from="25" to="17" numLanes="2" width="5.375" shape="-105.000000,4.499851 -104.804995,8.469555 -104.221829,12.401030 -103.256119,16.256414 -101.917164,19.998576 -100.217859,23.591478 -98.174571,27.000517 -95.806976,30.192863 -93.137877,33.137772 -90.192978,35.806882 -87.000641,38.174488 -83.591609,40.217789 -79.998713,41.917107 -76.256556,43.256076 -72.401176,44.221800 -68.469703,44.804981 -64.500000,45.000000 "/>
  <edge id="-0" to="25" from="17" numLanes="2" width="5.375" shape="-64.500000,45.000000 -68.469703,44.804981 -72.401176,44.221800 -76.256556,43.256076 -79.998713,41.917107 -83.591609,40.217789 -87.000641,38.174488 -90.192978,35.806882 -93.137877,33.137772 -95.806976,30.192863 -98.174571,27.000517 -100.217859,23.591478 -101.917164,19.998576 -103.256119,16.256414 -104.221829,12.401030 -104.804995,8.469555 -105.000000,4.499851 "/>
  <edge id="2" from="23" to="24" numLanes="2" width="5.375" shape="-64.501322,-105.000000 -68.471020,-104.804851 -72.402473,-104.221542 -76.257822,-103.255692 -79.999935,-101.916601 -83.592775,-100.217166 -87.001740,-98.173754 -90.194001,-95.806043 -93.138812,-93.136837 -95.807815,-90.191841 -98.175306,-86.999418 -100.218483,-83.590311 -101.917670,-79.997354 -103.256503,-76.255148 -104.222087,-72.399733 -104.805125,-68.468239 -105.000000,-64.498529 "/>
  <edge id="-2" to="23" from="24" numLanes="2" width="5.375" shape="-105.000000,-64.498529 -104.805125,-68.468239 -104.222087,-72.399733 -103.256503,-76.255148 -101.917670,-79.997354 -100.218483,-83.590311 -98.175306,-86.999418 -95.807815,-90.191841 -93.138812,-93.136837 -90.194001,-95.806043 -87.001740,-98.173754 -83.592775,-100.217166 -79.999935,-101.916601 -76.257822,-103.255692 -72.402473,-104.221542 -68.471020,-104.804851 -64.501322,-105.000000 "/>
  <edge id="4" from="16" to="22" numLanes="2" width="5.375" shape="44.999949,-64.564354 44.798622,-68.533742 44.209195,-72.464283 43.237346,-76.318124 41.892432,-80.058149 40.187408,-83.648340 38.138693,-87.054120 35.766017,-90.242692 33.092231,-93.183346 30.143085,-95.847762 26.946981,-98.210281 23.534699,-100.248150 19.939101,-101.941743 16.194816,-103.274750 12.337903,-104.234334 8.405507,-104.811252 4.435498,-104.999949 "/>
  <edge id="-4" to="16" from="22" numLanes="2" width="5.375" shape="4.435498,-104.999949 8.405507,-104.811252 12.337903,-104.234334 16.194816,-103.274750 19.939101,-101.941743 23.534699,-100.248150 26.946981,-98.210281 30.143085,-95.847762 33.092231,-93.183346 35.766017,-90.242692 38.138693,-87.054120 40.187408,-83.648340 41.892432,-80.058149 43.237346,-76.318124 44.209195,-72.464283 44.798622,-68.533742 44.999949,-64.564354 "/>
  <edge id="6" from="29" to="28" numLanes="2" width="5.375" shape="4.500000,45.000000 8.469703,44.804981 12.401176,44.221800 16.256556,43.256076 19.998713,41.917107 23.591609,40.217789 27.000641,38.174488 30.192978,35.806882 33.137877,33.137772 35.806976,30.192863 38.174571,27.000517 40.217859,23.591478 41.917164,19.998576 43.256119,16.256414 44.221829,12.401030 44.804995,8.469555 45.000000,4.499851 "/>
  <edge id="-6" to="29" from="28" numLanes="2" width="5.375" shape="45.000000,4.499851 44.804995,8.469555 44.221829,12.401030 43.256119,16.256414 41.917164,19.998576 40.217859,23.591478 38.174571,27.000517 35.806976,30.192863 33.137877,33.137772 30.192978,35.806882 27.000641,38.174488 23.591609,40.217789 19.998713,41.917107 16.256556,43.256076 12.401176,44.221800 8.469703,44.804981 4.500000,45.000000 "/>
  <edge id="9" from="27" to="26" numLanes="2" width="5.375" shape="-4.501322,-45.000000 -8.471020,-44.804851 -12.402473,-44.221542 -16.257822,-43.255692 -19.999935,-41.916601 -23.592775,-40.217166 -27.001740,-38.173754 -30.194001,-35.806043 -33.138812,-33.136837 -35.807815,-30.191841 -38.175306,-26.999418 -40.218483,-23.590311 -41.917670,-19.997354 -43.256503,-16.255148 -44.222087,-12.399733 -44.805125,-8.468239 -45.000000,-4.498529 "/>
  <edge id="-9" to="27" from="26" numLanes="2" width="5.375" shape="-45.000000,-4.498529 -44.805125,-8.468239 -44.222087,-12.399733 -43.256503,-16.255148 -41.917670,-19.997354 -40.218483,-23.590311 -38.175306,-26.999418 -35.807815,-30.191841 -33.138812,-33.136837 -30.194001,-35.806043 -27.001740,-38.173754 -23.592775,-40.217166 -19.999935,-41.916601 -16.257822,-43.255692 -12.402473,-44.221542 -8.471020,-44.804851 -4.501322,-45.000000 "/>
  <edge id="11" from="21" to="16" numLanes="2" width="5.375" shape="105.000000,-4.500149 104.804966,-8.469851 104.221771,-12.401322 103.256032,-16.256698 101.917050,-19.998851 100.217719,-23.591740 98.174406,-27.000765 95.806788,-30.193093 93.137667,-33.137982 90.192748,-35.807071 87.000393,-38.174654 83.591346,-40.217930 79.998438,-41.917221 76.256271,-43.256162 72.400884,-44.221858 68.469407,-44.805010 64.499702,-45.000000 "/>
  <edge id="-11" to="21" from="16" numLanes="2" width="5.375" shape="64.499702,-45.000000 68.469407,-44.805010 72.400884,-44.221858 76.256271,-43.256162 79.998438,-41.917221 83.591346,-40.217930 87.000393,-38.174654 90.192748,-35.807071 93.137667,-33.137982 95.806788,-30.193093 98.174406,-27.000765 100.217719,-23.591740 101.917050,-19.998851 103.256032,-16.256698 104.221771,-12.401322 104.804966,-8.469851 105.000000,-4.500149 "/>
  <edge id="13" from="19" to="20" numLanes="2" width="5.375" shape="64.500000,105.000000 68.469703,104.804981 72.401176,104.221800 76.256556,103.256076 79.998713,101.917107 83.591609,100.217789 87.000641,98.174488 90.192978,95.806882 93.137877,93.137772 95.806976,90.192863 98.174571,87.000517 100.217859,83.591478 101.917164,79.998576 103.256119,76.256414 104.221829,72.401030 104.804995,68.469555 105.000000,64.499851 "/>
  <edge id="-13" to="19" from="20" numLanes="2" width="5.375" shape="105.000000,64.499851 104.804995,68.469555 104.221829,72.401030 103.256119,76.256414 101.917164,79.998576 100.217859,83.591478 98.174571,87.000517 95.806976,90.192863 93.137877,93.137772 90.192978,95.806882 87.000641,98.174488 83.591609,100.217789 79.998713,101.917107 76.256556,103.256076 72.401176,104.221800 68.469703,104.804981 64.500000,105.000000 "/>
  <edge id="15" from="17" to="18" numLanes="2" width="5.375" shape="-45.000000,64.499851 -44.804995,68.469555 -44.221829,72.401030 -43.256119,76.256414 -41.917164,79.998576 -40.217859,83.591478 -38.174571,87.000517 -35.806976,90.192863 -33.137877,93.137772 -30.192978,95.806882 -27.000641,98.174488 -23.591609,100.217789 -19.998713,101.917107 -16.256556,103.256076 -12.401176,104.221800 -8.469703,104.804981 -4.500000,105.000000 "/>
  <edge id="-15" to="17" from="18" numLanes="2" width="5.375" shape="-4.500000,105.000000 -8.469703,104.804981 -12.401176,104.221800 -16.256556,103.256076 -19.998713,101.917107 -23.591609,100.217789 -27.000641,98.174488 -30.192978,95.806882 -33.137877,93.137772 -35.806976,90.192863 -38.174571,87.000517 -40.217859,83.591478 -41.917164,79.998576 -43.256119,76.256414 -44.221829,72.401030 -44.804995,68.469555 -45.000000,64.499851 "/>
</edges>
