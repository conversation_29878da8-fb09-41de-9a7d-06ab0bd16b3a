#VRML_SIM R2023b utf8
# license: Creative Commons Attribution 4.0 International License.
# license url: https://creativecommons.org/licenses/by/4.0/legalcode
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/objects/trees/protos/Cypress.proto
# keywords: exterior/tree
# A cypress tree with optional boundingObject.
# template language: javascript

PROTO Cypress [
  field SFVec3f    translation           0 0 0
  field SFRotation rotation              0 0 1 0
  field SFString   name                  "cypress tree"
  field SFFloat    scale                 1
  field SFBool     enableBoundingObject  TRUE            # Defines whether the tree should have a bounding object.
]
{
  Solid {
    translation IS translation
    rotation IS rotation
    name IS name
    model "cypress tree"
    recognitionColors [
      0 0.16470588235294117 0
      0.30196078431372547 0.2196078431372549 0.12156862745098039
    ]
    children [
      Transform {
        scale %<= fields.scale.value >% %<= fields.scale.value >% %<= fields.scale.value >%
        children [
          Shape {
            appearance PBRAppearance {
              baseColorMap ImageTexture {
                url [
                  "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/textures/cypress_trunk.jpg"
                ]
              }
              normalMap ImageTexture {
                url [
                  "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/textures/cypress_trunk_normal.jpg"
                ]
              }
              roughness 0.85
              metalness 0
            }
            geometry IndexedFaceSet {
              coord Coordinate {
                point [
                  -0.100485 0 0 0.051312 -0.088875 0 0.034892 0.029341 4.452566 0.04956 0.020858 4.452099 0.054597 0.094565 0 0.049545 0.037799 4.451528
                ]
              }
              texCoord TextureCoordinate {
                point [
                  0 0
                  0.3333 0
                  0 4
                  0 4
                  0.3333 0
                  0.3333 4
                  0.3333 0
                  0.6665 0
                  0.3333 4
                  0.3333 4
                  0.6665 0
                  0.6665 4
                  0.6665 0
                  1 0
                  0.6665 4
                  0.6665 4
                  1 0
                  1 4
                ]
              }
              coordIndex [
                0, 1, 2, -1, 2, 1, 3, -1, 1, 4
                3, -1, 3, 4, 5, -1, 4, 0, 5, -1
                5, 0, 2, -1
              ]
              texCoordIndex [
                0, 1, 2, -1, 3, 4, 5, -1, 6, 7
                8, -1, 9, 10, 11, -1, 12, 13, 14, -1
                15, 16, 17, -1
              ]
            }
          }
          Shape {
            appearance PBRAppearance {
              baseColorMap ImageTexture {
                url [
                  "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/textures/cypress_leaves.png"
                ]
              }
              metalness 0
              roughness 1
            }
            geometry IndexedFaceSet {
              coord Coordinate {
                point [
                  0 0 0.241798 0.09005 0.295886 0.264063 -0.253655 0.002355 1.404111 -0.162412 0.298176 1.420464 -0.20986 -0.222007 0.189721 -0.466426 -0.219696 1.365058 -0.149974 0.139528 0.204501 -0.405629 0.141835 1.375631 0.071683 -0.194668 0.259624 -0.181194 -0.192376 1.417942 0.000102 0 0.2817 -0.233125 -0.08704 0.323715 0.254545 -0.327798 1.361063 0.015663 -0.407539 1.379091 0.233853 -0.021423 0.188711 0.501126 -0.366007 1.322515 -0.008705 -0.31066 0.139505 0.265769 -0.664624 1.30387 0.233452 0.247671 0.314703 0.482986 -0.0738 1.373229 0.000121 0 0.32203 0.337543 -0.103428 0.359831 0.021473 0.38489 1.503583 0.358618 0.275783 1.524208 -0.236573 0.245042 0.222201 -0.214527 0.643862 1.446715 0.205238 0.145605 0.256344 0.227025 0.539205 1.464843 -0.241688 -0.085987 0.363102 -0.220601 0.293463 1.528221 0.00014 0 0.362793 -0.074349 0.275824 0.42322 -0.338739 -0.293667 1.633436 -0.405418 -0.011188 1.664868 0.044451 -0.279145 0.290049 -0.303852 -0.581044 1.595675 -0.272524 0.054494 0.278987 -0.62241 -0.248772 1.590491 0.251225 -0.137214 0.412113 -0.081136 -0.425342 1.658554 0.000186 0.000122 0.443407 0.287616 -0.02184 0.571158 -0.244642 0.406454 1.48456 0.062672 0.351876 1.529266 -0.278606 0.046312 0.303549 -0.545158 0.48828 1.435436 0.036229 0.244971 0.300784 -0.230369 0.687013 1.432848 -0.196235 -0.200874 0.494504 -0.433483 0.193025 1.504013 0.000161 0.000106 0.402844 -0.118996 -0.266503 0.451246 0.384044 -0.030116 1.491613 0.256323 -0.296055 1.515807 0.233105 0.146655 0.293839 0.636253 0.114928 1.437072 0.195738 -0.126952 0.301725 0.597481 -0.15857 1.440986 0.009944 0.254072 0.407778 0.392965 0.223918 1.494113 0.00051 0.000335 0.819925 -0.199838 0.008495 0.876191 0.09378 -0.492018 1.761288 -0.111551 -0.457787 1.767522 0.158298 -0.067063 0.701892 0.262015 -0.614064 1.748133 -0.186932 -0.173263 0.653442 -0.078939 -0.74263 1.74261 0.288026 0.091869 0.86401 0.377404 -0.380127 1.766306 0.00024 0.000158 0.52424 0.018693 -0.216509 0.579001 0.407181 0.219902 1.660146 0.413734 -0.003202 1.68172 0.071083 0.189955 0.428107 0.49888 0.420981 1.622174 0.243874 -0.030824 0.398667 0.678007 0.203629 1.610402 -0.214518 0.157751 0.595859 0.17694 0.36912 1.688589 0.000211 0.000138 0.483783 -0.174439 0.109665 0.532505 -0.051062 -0.414541 1.554285 -0.224413 -0.295002 1.577356 0.081919 -0.172434 0.39205 0.028145 -0.606386 1.511924 -0.133932 -0.137492 0.395208 -0.187666 -0.571141 1.514312 0.207249 0.029351 0.516395 0.156917 -0.378077 1.568314 0.000302 0.000198 0.605749 -0.217774 0.017236 0.676643 0.180709 -0.385631 1.66047 -0.045359 -0.351586 1.685112 0.207741 -0.048297 0.519201 0.397898 -0.454875 1.630342 -0.057674 -0.223647 0.489413 0.135796 -0.637276 1.619724 0.16505 0.188741 0.671416 0.3381 -0.181433 1.683567 0.000269 0.000177 0.564478 0.168752 0.169691 0.629316 -0.40071 0.134572 1.624591 -0.217431 0.299126 1.650209 -0.201551 -0.10608 0.468023 -0.624517 0.035686 1.586417 -0.126981 0.215339 0.448897 -0.554252 0.358548 1.5787 -0.005652 -0.270739 0.613626 -0.395481 -0.140082 1.644182 0.000337 0.000221 0.646053 0.222723 -0.156904 0.710696 0.171941 0.452395 1.684532 0.385073 0.271263 1.693936 -0.098274 0.209667 0.488794 0.096113 0.720948 1.663281 0.205443 0.032384 0.542754 0.392179 0.523814 1.671566 -0.223729 0.034808 0.694754 -0.059337 0.46827 1.690176 0.000373 0.000245 0.686596 -0.020575 0.17684 0.7486 -0.368074 -0.259541 1.723773 -0.374032 -0.072402 1.744088 -0.020844 -0.16721 0.603063 -0.409512 -0.44122 1.696476 -0.220137 0.006512 0.556457 -0.620186 -0.275502 1.681522 0.208322 -0.132291 0.755818 -0.143268 -0.380221 1.746113 0.000417 0.000274 0.731477 -0.081637 -0.171562 0.781864 0.495797 -0.098538 1.75146 0.392624 -0.266161 1.759043 0.124501 0.108183 0.623041 0.665426 0.000284 1.735338 0.127328 -0.242589 0.545618 0.700931 -0.357008 1.724151 0.015246 0.326684 0.785044 0.487985 0.232389 1.759137 0.000464 0.000305 0.778033 0.270373 0.239932 0.80297 -0.393349 0.395068 1.862366 -0.115258 0.62648 1.865178 -0.289869 -0.038228 0.565627 -0.752936 0.426073 1.837269 -0.057828 0.197642 0.561786 -0.522096 0.663148 1.83668 -0.156626 -0.242741 0.851202 -0.526639 0.128124 1.871159 0.001033 0.000678 1.199815 0.230525 -0.164541 1.218714 0.266281 0.398951 2.142167 0.489807 0.224733 2.139716 -0.072097 0.193406 1.005317 0.25267 0.681431 2.160632 0.200016 -0.01587 1.04572 0.512206 0.453191 2.156038 -0.221931 0.093953 1.27193 0.021457 0.459259 2.136059 0.000544 0.000357 0.849422 0.22913 -0.224023 0.886324 0.307142 0.407968 1.999823 0.527686 0.172871 2.006842 -0.022746 0.270466 0.674899 0.321457 0.728192 1.965025 0.309367 0.066624 0.644143 0.660061 0.533001 1.958383 -0.333429 0.164195 0.908769 -0.03948 0.554948 2.012174 0.000607 0.000398 0.903224 0.14271 0.216041 1.049579 -0.499869 0.060648 1.901489 -0.287361 0.267816 1.907547 -0.149775 -0.200124 0.744192 -0.726738 -0.130667 1.894876 -0.068086 0.157244 0.793337 -0.621277 0.223839 1.896647 0.040839 -0.183113 0.981322 -0.422206 -0.127369 1.904997 0.000645 0.000424 0.933001 -0.234461 -0.179822 0.998331 0.359372 -0.303733 1.931087 0.103261 -0.466191 1.937839 0.253908 0.096563 0.796449 0.656653 -0.244869 1.91729 -0.032896 -0.15954 0.85169 0.352129 -0.485966 1.923114 0.05519 0.161823 0.998575 0.392696 -0.124363 1.937481 0.000702 0.000461 0.977664 0.258338 -0.08896 1.055307 0.037385 0.510796 2.03678 0.292519 0.387338 2.044919 -0.22483 0.124754 0.858972 -0.18432 0.68713 2.024362 0.206662 0.131337 0.816666 0.248538 0.712293 2.02001 -0.271108 -0.060189 1.066599 -0.237294 0.411125 2.046021 0.000789 0.000518 1.040523 -0.107334 0.218208 1.136214 -0.441938 -0.359616 2.136051 -0.506718 -0.10675 2.126141 0.080114 -0.211731 0.910546 -0.421534 -0.619685 2.149628 -0.15422 0.002389 0.824783 -0.694947 -0.43728 2.159079 0.177994 -0.195714 1.066342 -0.252894 -0.546239 2.133022 0.000836 0.000549 1.073256 -0.082644 -0.15681 1.156506 0.48243 -0.074368 2.028618 0.355978 -0.224918 2.02615 0.108361 0.099505 0.941698 0.65813 0.013785 2.033052 0.060231 -0.137041 0.955497 0.603255 -0.221692 2.033396 -0.011146 0.181183 1.132868 0.439151 0.111225 2.025801 0.000901 0.000592 1.117064 0.282975 0.121996 1.264357 -0.330266 0.388345 2.219555 0.003922 0.449183 2.19472 -0.237548 -0.001777 0.873885 -0.654313 0.485455 2.25909 0.036768 0.165654 0.958699 -0.34977 0.617757 2.244068 -0.097146 -0.177922 1.227204 -0.389946 0.165242 2.202977 0.001674 0.001099 1.534711 0.210325 -0.194431 1.61513 0.290273 0.40894 2.50155 0.470516 0.173158 2.489144 -0.048257 0.178286 1.301589 0.322277 0.702222 2.536134 0.161424 0.001995 1.349267 0.515051 0.501974 2.528565 -0.206647 0.091976 1.625564 0.050192 0.454814 2.488637 0.000969 0.000636 1.159456 -0.260167 0.066454 1.274937 0.009721 -0.506522 2.205096 -0.252539 -0.376524 2.191685 0.210924 -0.10279 0.983157 0.221395 -0.708073 2.225855 -0.108619 -0.105513 0.986902 -0.09818 -0.70895 2.225891 0.224393 0.032132 1.20573 0.23269 -0.44906 2.199225 0.001164 0.000764 1.276294 -0.112317 -0.203453 1.272646 0.395409 -0.217646 2.23584 0.284112 -0.423087 2.237521 0.158494 0.032669 1.106514 0.628374 -0.228128 2.250443 0.060802 -0.208735 1.05772 0.553103 -0.482098 2.256312 0.112319 0.28225 1.336556 0.478918 0.079333 2.228702 0.00109 0.000716 1.233878 -0.062408 0.230096 1.24296 -0.486312 -0.143183 2.259201 -0.544876 0.087681 2.257929 -0.100512 -0.117213 1.053603 -0.67573 -0.287529 2.263031 -0.206783 0.060916 0.986149 -0.814351 -0.119131 2.263391 0.151227 -0.279736 1.317608 -0.296156 -0.411596 2.259041 0.001417 0.000931 1.411936 -0.003621 -0.215333 1.494562 0.539127 0.093793 2.411371 0.484553 -0.13105 2.401886 0.051478 0.16685 1.258355 0.681345 0.275673 2.429155 0.114607 -0.066139 1.213521 0.771476 0.047359 2.434531 -0.067693 0.220419 1.47299 0.43328 0.306919 2.404114 0.001239 0.000813 1.317986 0.267128 0.016057 1.339006 -0.050504 0.441705 2.273766 0.216607 0.446175 2.271892 -0.13427 0.121677 1.122025 -0.197498 0.663926 2.293319 0.091808 0.129684 1.147032 0.030037 0.659079 2.291009 -0.230236 -0.016685 1.305775 -0.282684 0.430436 2.274786 0.001339 0.00088 1.370877 -0.106996 0.217093 1.504108 -0.35979 -0.334835 2.356786 -0.409644 -0.064386 2.332147 0.055008 -0.195658 1.184523 -0.387995 -0.607302 2.391448 -0.198442 0.044881 1.139185 -0.661563 -0.385421 2.400422 0.234346 -0.151424 1.505195 -0.067569 -0.432223 2.331251 0.002395 0.001573 1.82783 -0.22327 0.094942 1.961106 -0.070116 -0.420721 2.774551 -0.281955 -0.245817 2.730538 0.18366 -0.104376 1.645286 0.092198 -0.638438 2.83503 -0.20363 -0.040753 1.638345 -0.295861 -0.579353 2.837934 0.27889 -0.023116 1.883358 0.212185 -0.411169 2.755645 0.001865 0.001225 1.619319 -0.173214 -0.167822 1.670127 0.349064 -0.294522 2.480842 0.14979 -0.44299 2.473222 0.141207 0.020396 1.414313 0.58689 -0.359115 2.51366 -0.012458 -0.130091 1.455886 0.413445 -0.492779 2.507462 0.123423 0.168756 1.655595 0.452972 -0.111979 2.474502 0.001485 0.000975 1.445033 0.123549 0.161889 1.514652 -0.433474 0.200218 2.376092 -0.276608 0.345172 2.371526 -0.133734 -0.115656 1.327762 -0.62717 0.110402 2.383475 -0.018347 0.195029 1.290258 -0.530157 0.429512 2.385139 -0.020578 -0.220079 1.557521 -0.399758 -0.046415 2.369678 0.001759 0.001155 1.572386 0.011279 0.358199 1.638468 -0.504766 -0.036147 2.649749 -0.4577 0.323739 2.635446 -0.136395 -0.201127 1.304182 -0.79347 -0.249825 2.703874 -0.134999 0.184221 1.360069 -0.760197 0.137935 2.691516 0.049367 -0.304458 1.612741 -0.435 -0.340082 2.642663 0.001574 0.001033 1.487933 -0.193665 -0.006851 1.541111 0.108371 -0.507143 2.476532 -0.093014 -0.486039 2.473892 0.17004 -0.030869 1.380185 0.289315 -0.597897 2.482104 -0.103007 -0.128883 1.315526 0.023797 -0.731421 2.485812 0.236149 0.051158 1.489012 0.342795 -0.456307 2.476246 0.001945 0.001277 1.652292 0.175801 -0.054994 1.753605 -0.011513 0.539871 2.681476 0.163851 0.426425 2.675631 -0.152308 0.071014 1.529819 -0.167585 0.678619 2.688343 0.237297 0.098576 1.504131 0.22166 0.719855 2.688272 -0.259438 -0.075124 1.764771 -0.271245 0.400856 2.676613 0.002045 0.001343 1.693813 -0.142153 0.256695 1.714173 -0.378636 -0.235645 2.63365 -0.513273 0.025651 2.630576 -0.003993 -0.215087 1.494982 -0.478468 -0.510386 2.664687 -0.194988 0.077569 1.481889 -0.675695 -0.221605 2.666868 0.192369 -0.21581 1.772336 -0.151216 -0.429735 2.621257 0.002172 0.001427 1.744258 -0.130724 -0.204428 1.862497 0.409519 -0.150124 2.730318 0.210781 -0.331291 2.689091 0.139991 0.180683 1.59296 0.631784 -0.002532 2.783542 0.038755 -0.14041 1.545359 0.557987 -0.333913 2.802399 0.073673 0.234629 1.78326 0.458606 0.091482 2.715035 0.002278 0.001496 1.784171 0.285927 0.167114 1.841538 -0.291607 0.426721 2.780128 0.012736 0.562283 2.766896 -0.235427 -0.03915 1.553908 -0.611613 0.505603 2.830638 0.042111 0.140776 1.576856 -0.325657 0.673304 2.824872 -0.205451 -0.142771 1.78481 -0.499285 0.282378 2.78059 0.002505 0.001645 1.86662 0.16885 -0.162792 1.884332 0.284445 0.303128 2.815396 0.443489 0.130857 2.80822 -0.034464 0.143694 1.683647 0.321294 0.524386 2.884034 0.227163 -0.096641 1.680467 0.583951 0.285157 2.88437 -0.238181 0.167867 1.962668 0.005261 0.42804 2.780215 0.002614 0.001717 1.905013 0.047872 0.227396 1.970159 -0.430251 0.02695 2.956724 -0.348303 0.25047 2.933339 -0.089199 -0.14313 1.73824 -0.61557 -0.112393 3.01558 -0.0755 0.108864 1.737643 -0.601961 0.139606 3.0152 0.025956 -0.199114 1.973697 -0.368652 -0.176132 2.933095 0.002736 0.001797 1.945966 -0.266052 -0.163462 2.059066 0.30277 -0.331933 2.991069 -0.0092 -0.449297 2.955721 0.208275 0.010486 1.68134 0.609934 -0.435959 3.075793 -0.013827 -0.151309 1.736589 0.366863 -0.574498 3.058958 0.164965 0.192581 2.015516 0.43799 -0.111191 2.967771 0.002875 0.001888 1.991032 0.253306 -0.037062 2.048637 0.025978 0.339733 2.83982 0.274063 0.266678 2.81397 -0.181623 0.07992 1.825434 -0.151831 0.514976 2.912104 0.14279 0.056958 1.831422 0.172306 0.488005 2.908287 -0.21434 0.003648 2.021007 -0.192421 0.324285 2.827685 0.004229 0.002777 2.361881 -0.142513 0.234401 2.46736 -0.251414 -0.222716 3.304256 -0.354556 0.047263 3.251515 0.076072 -0.200302 2.162117 -0.262301 -0.498573 3.404718 -0.138656 0.032905 2.14183 -0.4856 -0.272906 3.41554 0.189576 -0.178358 2.412697 -0.044866 -0.385201 3.278133 0.002985 0.00196 2.026184 -0.064803 0.156092 2.107156 -0.362547 -0.293304 3.03272 -0.394292 -0.110053 3.014846 0.05122 -0.146607 1.923668 -0.359954 -0.478745 3.055372 -0.221624 0.129581 1.852234 -0.664671 -0.228307 3.071345 0.223093 -0.204796 2.10234 -0.108476 -0.472621 3.015735 0.003141 0.002063 2.07345 -0.080171 -0.166967 2.116798 0.350554 -0.126962 2.910537 0.242763 -0.286819 2.895774 0.099062 0.117831 1.951843 0.516174 -0.037313 2.954404 -0.032282 -0.218842 1.956353 0.383196 -0.373373 2.95505 0.047862 0.229229 2.174252 0.336574 0.122201 2.871965 0.00328 0.002154 2.114903 0.246255 0.031829 2.266604 -0.126047 0.353219 3.116126 0.147152 0.302059 3.040789 -0.208118 0.003518 1.919353 -0.376278 0.45844 3.212272 0.082033 0.152416 1.835191 -0.102361 0.650755 3.250033 -0.261405 -0.150705 2.231502 -0.368069 0.139747 3.062508 0.003411 0.00224 2.15205 -0.272007 0.115548 2.189553 -0.138067 -0.420793 3.17622 -0.406691 -0.287253 3.165811 0.084218 -0.15056 1.906647 -0.102773 -0.709131 3.251823 -0.192109 -0.018345 1.980609 -0.365537 -0.536528 3.230133 0.249232 -0.044102 2.225997 0.121634 -0.4258 3.152264 0.003574 0.002347 2.196748 0.088664 -0.232483 2.260681 0.421661 0.205618 3.160686 0.470316 -0.046913 3.142428 0.0562 0.106475 1.966834 0.605655 0.373569 3.227108 0.120069 -0.058816 2.002967 0.648946 0.19828 3.216822 -0.120932 0.182528 2.279792 0.249608 0.3627 3.136472 0.003728 0.002448 2.238171 0.100398 0.174421 2.324517 -0.327007 0.118019 3.102139 -0.182385 0.273248 3.065209 -0.10049 -0.094888 2.076803 -0.520378 0.051815 3.169964 0.003216 0.212953 2.073793 -0.417693 0.360013 3.16958 -0.049004 -0.248486 2.312898 -0.339068 -0.147117 3.072307 0.003895 0.002558 2.280329 -0.190549 -0.104742 2.310116 0.226397 -0.340923 3.187854 0.022496 -0.433432 3.179153 0.099606 -0.026917 2.07988 0.389464 -0.475751 3.26161 0.015019 -0.08416 2.068984 0.308814 -0.539151 3.266744 0.189839 0.131876 2.312145 0.401034 -0.193919 3.173626 0.005628 0.003696 2.647261 0.216558 -0.037035 2.699976 0.026122 0.359055 3.6169 0.235373 0.290434 3.596054 -0.066524 0.109339 2.466807 -0.040363 0.558821 3.684745 0.231045 0.067854 2.512881 0.255724 0.492726 3.665912 -0.241076 -0.04044 2.748139 -0.223714 0.262897 3.580526 0.004057 0.002664 2.321137 0.287434 -0.011647 2.340207 0.014054 0.363269 3.141476 0.297071 0.33684 3.1329 -0.173519 0.087403 2.115815 -0.159804 0.573359 3.222139 0.183644 0.067954 2.143452 0.196841 0.536444 3.209927 -0.196068 -0.054215 2.478621 -0.188904 0.210843 3.080973 0.004405 0.002893 2.402516 -0.003093 -0.196232 2.479549 0.397926 0.052742 3.306046 0.344042 -0.152275 3.27948 0.041281 0.163324 2.266341 0.517037 0.223618 3.353538 0.074186 -0.090468 2.262463 0.552605 -0.029835 3.355604 -0.044855 0.163379 2.473355 0.305583 0.207756 3.280666 0.004578 0.003007 2.441037 0.201212 0.145137 2.529735 -0.248257 0.247406 3.305965 -0.015437 0.354528 3.273517 -0.173538 -0.054158 2.262879 -0.498441 0.259964 3.369082 0.044329 0.163533 2.278871 -0.273532 0.470842 3.361501 -0.082 -0.175558 2.560476 -0.287105 0.022665 3.265606 0.005182 0.003403 2.564363 0.041814 0.235138 2.662336 -0.30445 -0.019554 3.451511 -0.217559 0.215893 3.410808 -0.084763 -0.150026 2.382673 -0.487225 -0.179838 3.525971 -0.08818 0.090441 2.410658 -0.476051 0.061706 3.513697 0.020304 -0.195812 2.563035 -0.290274 -0.218839 3.452791 0.004792 0.003147 2.485693 -0.236088 0.039404 2.501191 -0.035043 -0.316687 3.448071 -0.275002 -0.272656 3.440419 0.047371 -0.095515 2.266534 -0.00586 -0.528507 3.565793 -0.20693 -0.067676 2.258408 -0.260687 -0.50511 3.570882 0.320719 -0.021811 2.518374 0.282918 -0.324462 3.429605 0.004989 0.003277 2.52712 0.087942 -0.169537 2.627859 0.266191 0.207678 3.388509 0.304462 -0.00021 3.341475 -0.035548 0.15491 2.37793 0.291754 0.4112 3.457931 0.109472 -0.054178 2.415625 0.419958 0.188911 3.440009 -0.103937 0.075611 2.624853 0.114078 0.246111 3.343414 0.007967 0.005232 2.987016 0.24473 -0.054242 2.969977 0.080695 0.253375 3.878825 0.319622 0.20103 3.886461 -0.070524 0.142825 2.800491 0.026936 0.472468 3.973653 0.194946 0.055973 2.814264 0.290478 0.379263 3.965558 -0.243387 0.051841 3.032053 -0.176524 0.280655 3.857065 0.005422 0.003561 2.610005 -0.150108 -0.100919 2.670964 0.245889 -0.258109 3.558799 0.067645 -0.337916 3.532692 0.147273 0.058623 2.484834 0.434888 -0.254259 3.61435 -0.072109 -0.188981 2.397475 0.249323 -0.538596 3.65661 0.208227 0.214762 2.660166 0.429055 -0.025576 3.533691 0.006411 0.00421 2.77612 0.230965 0.080412 2.820572 -0.126598 0.316647 3.659557 0.107978 0.36944 3.639711 -0.152331 0.03145 2.596493 -0.324409 0.43513 3.730545 0.05801 0.129403 2.589575 -0.115159 0.535631 3.730618 -0.163837 -0.121789 2.867082 -0.277541 0.145561 3.62669 0.00588 0.003862 2.69028 -0.025298 0.19509 2.795806 -0.297241 -0.148529 3.541829 -0.278092 0.068224 3.505389 -0.002077 -0.161289 2.544204 -0.37473 -0.348942 3.591893 -0.15953 0.14593 2.544633 -0.531563 -0.041409 3.590573 0.157185 -0.196134 2.766871 -0.109869 -0.330232 3.516676 0.006125 0.004023 2.731719 -0.09288 -0.148433 2.810269 0.288684 -0.088933 3.583896 0.15241 -0.22908 3.552225 0.111152 0.140075 2.633362 0.440545 0.031652 3.624051 0.026539 -0.123041 2.622749 0.361838 -0.233414 3.630906 0.043555 0.165484 2.762793 0.31069 0.077622 3.569354 0.006993 0.004592 2.861936 0.102066 -0.226074 2.898871 0.24493 0.1231 3.791539 0.324492 -0.115289 3.770313 0.056969 0.126565 2.646912 0.385781 0.290319 3.917272 0.158594 -0.085568 2.653206 0.484845 0.076911 3.913961 -0.148703 0.193377 2.983588 0.037699 0.286225 3.719947 0.006678 0.004386 2.816966 -0.147322 0.09606 2.929885 -0.036315 -0.274382 3.66389 -0.181745 -0.128079 3.615763 0.130975 -0.096876 2.678521 0.077441 -0.442832 3.723513 -0.152403 -0.032915 2.704622 -0.204081 -0.367044 3.714759 0.184309 0.023336 2.903033 0.147997 -0.212839 3.624386 0.007308 0.004799 2.904714 0.100303 0.226716 2.943231 -0.271139 0.103551 3.807334 -0.158714 0.318599 3.785115 -0.116533 -0.124757 2.739617 -0.475383 0.002418 3.893562 -0.016472 0.162742 2.747652 -0.370423 0.288184 3.886283 -0.009967 -0.194336 3.005841 -0.240142 -0.112649 3.75757 0.011072 0.007272 3.292579 -0.20341 0.00731 3.285893 0.017407 -0.195911 4.125525 -0.196994 -0.199499 4.133668 0.110201 -0.11191 3.144199 0.117751 -0.369865 4.201144 -0.077294 -0.078426 3.180874 -0.070026 -0.323705 4.185979 0.176554 0.014248 3.306642 0.182753 -0.182818 4.114574 0.007606 0.004995 2.943721 -0.146358 -0.039458 3.043845 0.167986 -0.190308 3.782003 -0.014422 -0.200012 3.733642 0.153095 0.02942 2.825643 0.347146 -0.207017 3.839696 -0.033378 -0.152101 2.776485 0.175556 -0.40672 3.86823 0.160555 0.169764 3.005237 0.302593 -0.00313 3.747765 0.008708 0.005719 3.071903 -0.055172 -0.184157 3.078199 0.295665 -0.075706 4.010322 0.229866 -0.265031 4.010385 0.131133 0.078752 2.931007 0.479302 -0.02027 4.068441 0.099821 -0.185697 2.858185 0.480949 -0.294194 4.102787 0.079837 0.29685 3.100199 0.353161 0.219343 3.994292 0.008345 0.00548 3.032575 -0.084098 0.23215 3.070987 -0.197137 -0.09146 3.952098 -0.275095 0.142257 3.925665 -0.040216 -0.130084 2.830922 -0.319796 -0.263082 4.082101 -0.118295 0.116619 2.911499 -0.367912 -0.001799 4.028554 0.130617 -0.13957 3.116593 -0.044364 -0.221668 3.899608 0.009155 0.006013 3.119619 0.233277 0.048972 3.128277 -0.03331 0.239231 4.028686 0.191641 0.27759 4.019931 -0.098315 0.082794 2.933324 -0.155067 0.395278 4.142595 0.188919 0.129481 2.960075 0.134423 0.429447 4.121946 -0.236214 -0.083269 3.189164 -0.273561 0.121553 3.990672 0.009591 0.006299 3.162516 -0.170599 0.137555 3.189835 -0.10891 -0.192817 4.021958 -0.283164 -0.051711 4.009014 0.086035 -0.14159 3.011758 -0.066763 -0.397617 4.103953 -0.144061 0.006781 3.017605 -0.29591 -0.247671 4.103386 0.196487 -0.080929 3.208009 0.088688 -0.262287 3.994799 0.010034 0.00659 3.203841 -0.007528 -0.202621 3.266727 0.24566 0.042609 4.041354 0.202757 -0.170434 4.012993 0.061673 0.191584 3.09828 0.34026 0.2341 4.09049 0.130285 -0.124538 3.061671 0.424745 -0.079621 4.111032 -0.065251 0.211001 3.270289 0.142509 0.242807 4.00747 0.010532 0.006917 3.24835 0.14193 0.129824 3.25211 -0.145783 0.186701 4.062315 -0.012478 0.307458 4.056548 -0.099466 0.040859 3.110024 -0.295702 0.265642 4.122966 -0.020697 0.151247 3.076012 -0.226072 0.386332 4.134485 -0.133485 -0.165356 3.281338 -0.281369 0.004925 4.053312 0.012276 0.008062 3.383962 0.081595 0.133798 3.428996 -0.182104 0.062868 4.205076 -0.096074 0.183906 4.180757 -0.071706 -0.096701 3.316031 -0.290934 -0.034907 4.24027 -0.076046 0.165666 3.237249 -0.322466 0.235105 4.27434 0.049672 -0.198098 3.485275 -0.109183 -0.15328 4.158957 0.011714 0.007693 3.344209 0.11449 -0.2026 3.426533 0.10478 0.074229 4.212488 0.189618 -0.148749 4.144321 0.027632 0.146118 3.138908 0.165446 0.244299 4.382663 0.167195 -0.037734 3.120997 0.30892 0.063212 4.397601 -0.168609 0.200166 3.424827 -0.093124 0.25427 4.145547 0.022241 0.014607 3.884669 0.196743 -0.101391 3.951647 0.063605 0.123861 4.524213 0.230851 -0.011923 4.479675 -0.126119 0.149158 3.793689 -0.075112 0.284713 4.581454 0.189514 0.124321 3.739616 0.245289 0.272882 4.600642 -0.220693 0.035223 3.924865 -0.182854 0.134864 4.510254 0.012963 0.008513 3.433339 -0.223139 -0.010667 3.562262 0.067999 -0.082958 4.284246 -0.184413 -0.074372 4.189655 0.21071 -0.025413 3.253122 0.288847 -0.156215 4.420601 -0.093691 -0.143274 3.284754 -0.018991 -0.268226 4.40508 0.176464 0.150937 3.53068 0.218403 0.081765 4.202166 0.014409 0.009463 3.524567 -0.0812 0.162222 3.535999 -0.079987 -0.0516 4.269515 -0.173048 0.102888 4.26055 -0.002904 -0.132272 3.414029 -0.120844 -0.209306 4.347403 -0.108838 0.001071 3.402821 -0.229045 -0.077502 4.354327 0.127699 -0.120251 3.54512 0.037553 -0.17843 4.256061 0.013675 0.008981 3.478652 0.214814 -0.084589 3.500825 0.08555 0.169754 4.270919 0.283102 0.068006 4.252912 -0.069794 0.152149 3.329523 0.023761 0.362358 4.364671 0.1408 0.064346 3.339104 0.232568 0.27048 4.354224 -0.167729 0.022631 3.553526 -0.10631 0.159561 4.228663 0.015233 0.010004 3.571187 -0.087487 -0.089177 3.647287 0.129134 -0.015408 4.296185 0.00562 -0.109705 4.251108 0.123689 0.091244 3.482774 0.261914 0.060119 4.349521 0.127986 -0.089884 3.41596 0.285773 -0.125604 4.396721 0.052182 0.220602 3.603888 0.155915 0.197578 4.269618 0.019466 0.012784 3.77619 0.158601 0.148522 3.880692 0.019203 0.027856 4.428087 0.162242 0.157323 4.320931 -0.127579 -0.088414 3.646417 -0.132634 -0.065645 4.558115 -0.067057 0.188702 3.592131 -0.07385 0.214264 4.5981 -0.057185 -0.2437 3.801729 -0.056735 -0.229773 4.414966 0.017121 0.011244 3.66969 -0.182365 0.133024 3.708566 -0.019863 -0.099319 4.359289 -0.216226 0.031875 4.340157 0.086246 -0.167146 3.529922 0.037278 -0.313834 4.442085 -0.148883 -0.068682 3.533943 -0.197778 -0.215149 4.444743 0.237056 -0.014484 3.72748 0.205317 -0.109238 4.319683 0.016183 0.010628 3.623213 0.181961 0.102749 3.599465 -0.001595 0.080314 4.275407 0.163092 0.176562 4.287527 -0.104245 0.061559 3.483801 -0.129815 0.160728 4.392137 0.071358 0.141556 3.48202 0.045914 0.240251 4.386245 -0.151385 -0.09255 3.660593 -0.167329 -0.029809 4.252438 0.018227 0.01197 3.722526 0.026755 -0.138773 3.759851 0.104264 0.039194 4.380263 0.103885 -0.114287 4.355107 0.070884 0.11779 3.621438 0.181728 0.152639 4.453195 0.140949 -0.169294 3.623795 0.251856 -0.134426 4.456014 -0.095627 0.206335 3.805741 -0.030644 0.227088 4.315757 0.020722 0.013609 3.828285 -0.145577 -0.003347 3.929291 0.085988 -0.036185 4.482689 -0.097753 -0.038717 4.416521 0.181146 0.013999 3.716888 0.265813 -0.051841 4.557274 0.061005 -0.103418 3.697913 0.150048 -0.172873 4.580299 0.149842 0.137343 3.846529 0.210802 0.091106 4.459599 0.023889 0.015689 3.944088 -0.09225 0.16619 3.959683 -0.008155 -0.016618 4.566642 -0.122892 0.135327 4.555233 0.013892 -0.142389 3.837817 -0.028207 -0.18505 4.653882 -0.13981 0.020552 3.835656 -0.182209 -0.022418 4.657474 0.164258 -0.12138 3.953446 0.133185 -0.152687 4.557308 0.028097 0.018452 4.074488 0.160915 0.01919 4.110809 -0.012118 0.137578 4.636954 0.124416 0.127277 4.622369 -0.087631 0.0639 4.015311 -0.133516 0.199869 4.655464 0.059984 0.170369 3.992574 0.012653 0.310634 4.652553 -0.088979 -0.105504 4.116123 -0.125962 0.004019 4.634289 0.025837 0.016968 4.007055 -0.038864 -0.130599 4.043372 0.148672 0.013699 4.586015 0.07422 -0.13356 4.578762 0.150665 0.080551 3.919557 0.29908 0.076476 4.6128 0.140544 -0.0476 3.916955 0.290649 -0.051729 4.617743 0.059399 0.1724 3.994436 0.18488 0.169048 4.585215 0.037331 0.024516 4.304299 0.078825 0.164324 4.313394 -0.023506 0.051648 4.784033 0.020878 0.190159 4.77006 -0.07808 -0.017435 4.22511 -0.155418 0.017096 4.836532 -0.063859 0.145067 4.210426 -0.143 0.180408 4.836246 0.019479 -0.157255 4.31928 -0.039636 -0.130896 4.785266 0.033732 0.022153 4.222671 0.123991 -0.179343 4.244089 0.124881 0.077547 4.734774 0.209692 -0.127242 4.726155 0.156873 0.138338 4.079343 0.285603 0.216453 4.79864 0.206178 0.016984 4.096023 0.330633 0.092514 4.79175 -0.056845 0.21527 4.205185 0.038727 0.273338 4.741666 0.030687 0.020153 4.146358 -0.120868 0.128985 4.178449 0.032015 -0.008507 4.728528 -0.119433 0.103323 4.708518 0.083993 -0.132053 4.047188 0.084954 -0.171094 4.809805 -0.129253 -0.077511 4.016285 -0.128428 -0.120405 4.845895 0.227904 -0.092798 4.126718 0.229177 -0.123013 4.735884 0.041476 0.027238 4.389447 -0.09218 -0.05225 4.403512 0.116493 -0.034995 4.897772 -0.01831 -0.113502 4.904463 0.114292 -0.075207 4.296802 0.209541 -0.154763 4.934893 0.075889 -0.102529 4.298229 0.171411 -0.182319 4.938069 0.174044 0.12073 4.384038 0.248229 0.059211 4.887037
                ]
              }
              texCoord TextureCoordinate {
                point [
                  0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0 1, 0.5 1, 0.5 0, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 1, 1 0, 1 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 1, 0.5 0, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 1, 0.5 0, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 1, 1 0, 1 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 1, 1 0, 1 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 1, 1 0, 1 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 1, 0.5 0, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 1, 1 0, 1 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 0, 1 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 1, 1 0, 1 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 0, 1 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 1, 0.5 0, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 1, 1 0, 1 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 1, 0.5 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 1, 1 0, 1 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 1, 1 0, 1 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0 1, 0.5 1, 0.5 0, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 1, 0.5 0, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 0, 1 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 1, 0.5 0, 0.5 1, 0 0, 0 1, 0.5 0, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 1, 1 0, 1 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0.5 1, 1 0, 1 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 0, 1 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0 1, 0.5 0, 0.5 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 0, 0 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0, 0 0, 0.5 0, 0 1, 0.5 0, 0.5 1, 1 0, 0.5 1, 1 1, 1 0, 0 1, 0.5 1, 0.5 0, 0.5 1, 1 0, 1 1, 0.5 0, 0.5 1, 1 0, 0 0, 0 1, 0.5 0, 0 1, 0.5 1, 0.5 0, 0.5 0, 1 0, 0.5 1, 0 0, 0.5 0, 0 1, 0 1, 0.5 0, 0.5 1, 0.5 1, 1 1, 1 0
                ]
              }
              coordIndex [
                2, 1, 3, -1, 4, 0, 5, -1, 0, 2, 1, -1, 5, 2, 0, -1, 0, 6, 2, -1, 8, 0, 9, -1, 2, 7, 6, -1, 9, 2, 0, -1, 10, 11, 12, -1, 15, 10, 12, -1, 12, 13, 11, -1, 14, 15, 10, -1, 10, 16, 12, -1, 19, 10, 12, -1, 12, 17, 16, -1, 18, 19, 10, -1, 20, 21, 22, -1, 24, 20, 25, -1, 25, 20, 22, -1, 22, 23, 21, -1, 22, 26, 27, -1, 28, 20, 29, -1, 20, 22, 26, -1, 29, 22, 20, -1, 34, 30, 35, -1, 30, 32, 31, -1, 32, 33, 31, -1, 35, 32, 30, -1, 30, 36, 32, -1, 38, 30, 39, -1, 32, 37, 36, -1, 39, 32, 30, -1, 42, 41, 43, -1, 40, 42, 41, -1, 44, 45, 40, -1, 45, 42, 40, -1, 40, 46, 42, -1, 48, 40, 49, -1, 49, 40, 42, -1, 42, 47, 46, -1, 54, 50, 55, -1, 55, 50, 52, -1, 50, 52, 51, -1, 52, 53, 51, -1, 50, 56, 52, -1, 52, 56, 57, -1, 58, 50, 59, -1, 59, 52, 50, -1, 64, 60, 65, -1, 60, 62, 61, -1, 62, 63, 61, -1, 65, 62, 60, -1, 60, 66, 62, -1, 68, 60, 69, -1, 62, 67, 66, -1, 69, 62, 60, -1, 70, 71, 72, -1, 74, 70, 75, -1, 75, 70, 72, -1, 72, 73, 71, -1, 70, 76, 72, -1, 72, 76, 77, -1, 78, 70, 79, -1, 79, 72, 70, -1, 84, 80, 85, -1, 85, 80, 82, -1, 80, 82, 81, -1, 82, 83, 81, -1, 80, 86, 82, -1, 89, 80, 82, -1, 82, 87, 86, -1, 88, 89, 80, -1, 92, 91, 93, -1, 90, 92, 91, -1, 94, 95, 90, -1, 95, 92, 90, -1, 90, 96, 92, -1, 98, 90, 99, -1, 99, 90, 92, -1, 92, 97, 96, -1, 100, 102, 101, -1, 102, 103, 101, -1, 104, 105, 100, -1, 105, 102, 100, -1, 102, 106, 107, -1, 109, 100, 102, -1, 100, 102, 106, -1, 108, 109, 100, -1, 110, 111, 112, -1, 112, 111, 113, -1, 115, 110, 112, -1, 114, 115, 110, -1, 110, 116, 112, -1, 112, 116, 117, -1, 119, 110, 112, -1, 118, 119, 110, -1, 122, 121, 123, -1, 125, 120, 122, -1, 120, 122, 121, -1, 124, 125, 120, -1, 120, 126, 122, -1, 129, 120, 122, -1, 122, 127, 126, -1, 128, 129, 120, -1, 130, 131, 132, -1, 135, 130, 132, -1, 132, 133, 131, -1, 134, 135, 130, -1, 130, 132, 136, -1, 132, 137, 136, -1, 138, 139, 130, -1, 139, 132, 130, -1, 140, 141, 142, -1, 144, 140, 145, -1, 145, 140, 142, -1, 142, 143, 141, -1, 142, 146, 147, -1, 148, 140, 149, -1, 140, 142, 146, -1, 149, 142, 140, -1, 152, 151, 153, -1, 150, 152, 151, -1, 154, 155, 150, -1, 155, 152, 150, -1, 159, 150, 152, -1, 150, 152, 156, -1, 152, 157, 156, -1, 158, 159, 150, -1, 160, 161, 162, -1, 164, 160, 165, -1, 165, 160, 162, -1, 162, 163, 161, -1, 168, 160, 169, -1, 160, 162, 166, -1, 162, 167, 166, -1, 169, 162, 160, -1, 172, 171, 173, -1, 170, 172, 171, -1, 174, 175, 170, -1, 175, 172, 170, -1, 170, 176, 172, -1, 178, 170, 179, -1, 179, 170, 172, -1, 172, 177, 176, -1, 184, 180, 185, -1, 180, 182, 181, -1, 182, 183, 181, -1, 185, 182, 180, -1, 180, 186, 182, -1, 188, 180, 189, -1, 189, 180, 182, -1, 182, 187, 186, -1, 190, 191, 192, -1, 192, 191, 193, -1, 195, 190, 192, -1, 194, 195, 190, -1, 190, 196, 192, -1, 192, 196, 197, -1, 199, 190, 192, -1, 198, 199, 190, -1, 202, 201, 203, -1, 204, 200, 205, -1, 205, 200, 202, -1, 200, 202, 201, -1, 200, 206, 202, -1, 202, 206, 207, -1, 208, 200, 209, -1, 209, 202, 200, -1, 212, 211, 213, -1, 214, 210, 215, -1, 210, 212, 211, -1, 215, 212, 210, -1, 210, 216, 212, -1, 218, 210, 219, -1, 212, 217, 216, -1, 219, 212, 210, -1, 222, 221, 223, -1, 225, 220, 222, -1, 220, 222, 221, -1, 224, 225, 220, -1, 220, 226, 222, -1, 229, 220, 222, -1, 222, 227, 226, -1, 228, 229, 220, -1, 230, 231, 232, -1, 235, 230, 232, -1, 232, 233, 231, -1, 234, 235, 230, -1, 230, 236, 232, -1, 239, 230, 232, -1, 232, 237, 236, -1, 238, 239, 230, -1, 240, 241, 242, -1, 242, 241, 243, -1, 245, 240, 242, -1, 244, 245, 240, -1, 249, 240, 242, -1, 240, 242, 246, -1, 242, 247, 246, -1, 248, 249, 240, -1, 250, 251, 252, -1, 255, 250, 252, -1, 252, 253, 251, -1, 254, 255, 250, -1, 250, 252, 256, -1, 252, 257, 256, -1, 258, 259, 250, -1, 259, 252, 250, -1, 260, 261, 262, -1, 264, 260, 265, -1, 265, 260, 262, -1, 262, 263, 261, -1, 262, 266, 267, -1, 268, 260, 269, -1, 260, 262, 266, -1, 269, 262, 260, -1, 272, 271, 273, -1, 270, 272, 271, -1, 274, 275, 270, -1, 275, 272, 270, -1, 279, 270, 272, -1, 270, 272, 276, -1, 272, 277, 276, -1, 278, 279, 270, -1, 280, 281, 282, -1, 285, 280, 282, -1, 282, 283, 281, -1, 284, 285, 280, -1, 280, 282, 286, -1, 282, 287, 286, -1, 288, 289, 280, -1, 289, 282, 280, -1, 290, 291, 292, -1, 292, 291, 293, -1, 294, 290, 295, -1, 295, 290, 292, -1, 292, 296, 297, -1, 290, 292, 296, -1, 298, 299, 290, -1, 299, 292, 290, -1, 300, 302, 301, -1, 302, 303, 301, -1, 304, 305, 300, -1, 305, 302, 300, -1, 309, 300, 302, -1, 300, 302, 306, -1, 302, 307, 306, -1, 308, 309, 300, -1, 310, 311, 312, -1, 314, 310, 315, -1, 315, 310, 312, -1, 312, 313, 311, -1, 318, 310, 319, -1, 310, 312, 316, -1, 312, 317, 316, -1, 319, 312, 310, -1, 320, 322, 321, -1, 322, 323, 321, -1, 324, 325, 320, -1, 325, 322, 320, -1, 329, 320, 322, -1, 320, 322, 326, -1, 322, 327, 326, -1, 328, 329, 320, -1, 330, 331, 332, -1, 334, 330, 335, -1, 335, 330, 332, -1, 332, 333, 331, -1, 338, 330, 339, -1, 330, 332, 336, -1, 332, 337, 336, -1, 339, 332, 330, -1, 342, 341, 343, -1, 340, 342, 341, -1, 344, 345, 340, -1, 345, 342, 340, -1, 340, 346, 342, -1, 348, 340, 349, -1, 349, 340, 342, -1, 342, 347, 346, -1, 350, 351, 352, -1, 352, 351, 353, -1, 355, 350, 352, -1, 354, 355, 350, -1, 350, 356, 352, -1, 352, 356, 357, -1, 359, 350, 352, -1, 358, 359, 350, -1, 364, 360, 365, -1, 365, 360, 362, -1, 360, 362, 361, -1, 362, 363, 361, -1, 360, 366, 362, -1, 368, 360, 369, -1, 369, 360, 362, -1, 362, 367, 366, -1, 370, 371, 372, -1, 372, 371, 373, -1, 375, 370, 372, -1, 374, 375, 370, -1, 370, 376, 372, -1, 372, 376, 377, -1, 379, 370, 372, -1, 378, 379, 370, -1, 380, 381, 382, -1, 382, 381, 383, -1, 384, 380, 385, -1, 385, 380, 382, -1, 382, 386, 387, -1, 380, 382, 386, -1, 388, 389, 380, -1, 389, 382, 380, -1, 394, 390, 395, -1, 395, 390, 392, -1, 390, 392, 391, -1, 392, 393, 391, -1, 390, 396, 392, -1, 398, 390, 399, -1, 399, 390, 392, -1, 392, 397, 396, -1, 400, 401, 402, -1, 402, 401, 403, -1, 405, 400, 402, -1, 404, 405, 400, -1, 400, 406, 402, -1, 402, 406, 407, -1, 409, 400, 402, -1, 408, 409, 400, -1, 410, 411, 412, -1, 412, 411, 413, -1, 415, 410, 412, -1, 414, 415, 410, -1, 410, 416, 412, -1, 412, 416, 417, -1, 419, 410, 412, -1, 418, 419, 410, -1, 424, 420, 425, -1, 425, 420, 422, -1, 420, 422, 421, -1, 422, 423, 421, -1, 420, 426, 422, -1, 428, 420, 429, -1, 429, 420, 422, -1, 422, 427, 426, -1, 432, 431, 433, -1, 435, 430, 432, -1, 430, 432, 431, -1, 434, 435, 430, -1, 430, 436, 432, -1, 439, 430, 432, -1, 432, 437, 436, -1, 438, 439, 430, -1, 440, 441, 442, -1, 445, 440, 442, -1, 442, 443, 441, -1, 444, 445, 440, -1, 440, 446, 442, -1, 449, 440, 442, -1, 442, 447, 446, -1, 448, 449, 440, -1, 450, 451, 452, -1, 452, 451, 453, -1, 455, 450, 452, -1, 454, 455, 450, -1, 459, 450, 452, -1, 450, 452, 456, -1, 452, 457, 456, -1, 458, 459, 450, -1, 460, 461, 462, -1, 462, 461, 463, -1, 465, 460, 462, -1, 464, 465, 460, -1, 460, 466, 462, -1, 462, 466, 467, -1, 469, 460, 462, -1, 468, 469, 460, -1, 474, 470, 475, -1, 475, 470, 472, -1, 470, 472, 471, -1, 472, 473, 471, -1, 470, 476, 472, -1, 479, 470, 472, -1, 472, 477, 476, -1, 478, 479, 470, -1, 480, 481, 482, -1, 484, 480, 485, -1, 485, 480, 482, -1, 482, 483, 481, -1, 480, 486, 482, -1, 482, 486, 487, -1, 488, 480, 489, -1, 489, 482, 480, -1, 494, 490, 495, -1, 495, 490, 492, -1, 490, 492, 491, -1, 492, 493, 491, -1, 490, 496, 492, -1, 499, 490, 492, -1, 492, 497, 496, -1, 498, 499, 490, -1, 502, 501, 503, -1, 500, 502, 501, -1, 504, 505, 500, -1, 505, 502, 500, -1, 500, 506, 502, -1, 508, 500, 509, -1, 509, 500, 502, -1, 502, 507, 506, -1, 510, 512, 511, -1, 512, 513, 511, -1, 514, 515, 510, -1, 515, 512, 510, -1, 512, 516, 517, -1, 519, 510, 512, -1, 510, 512, 516, -1, 518, 519, 510, -1, 524, 520, 525, -1, 525, 520, 522, -1, 520, 522, 521, -1, 522, 523, 521, -1, 520, 526, 522, -1, 522, 526, 527, -1, 528, 520, 529, -1, 529, 520, 522, -1, 534, 530, 535, -1, 530, 532, 531, -1, 532, 533, 531, -1, 535, 532, 530, -1, 530, 536, 532, -1, 538, 530, 539, -1, 532, 537, 536, -1, 539, 532, 530, -1, 540, 541, 542, -1, 542, 541, 543, -1, 545, 540, 542, -1, 544, 545, 540, -1, 540, 546, 542, -1, 542, 546, 547, -1, 549, 540, 542, -1, 548, 549, 540, -1, 550, 551, 552, -1, 552, 551, 553, -1, 555, 550, 552, -1, 554, 555, 550, -1, 552, 556, 557, -1, 558, 550, 559, -1, 559, 550, 552, -1, 550, 552, 556, -1, 560, 562, 561, -1, 562, 563, 561, -1, 564, 565, 560, -1, 565, 562, 560, -1, 560, 566, 562, -1, 568, 560, 569, -1, 562, 567, 566, -1, 569, 562, 560, -1, 570, 572, 571, -1, 572, 573, 571, -1, 574, 575, 570, -1, 575, 572, 570, -1, 570, 576, 572, -1, 572, 576, 577, -1, 578, 570, 579, -1, 579, 572, 570, -1, 580, 582, 581, -1, 582, 583, 581, -1, 584, 585, 580, -1, 585, 582, 580, -1, 589, 580, 582, -1, 580, 582, 586, -1, 582, 587, 586, -1, 588, 589, 580, -1, 590, 591, 592, -1, 594, 590, 595, -1, 595, 590, 592, -1, 592, 593, 591, -1, 598, 590, 599, -1, 590, 592, 596, -1, 592, 597, 596, -1, 599, 592, 590, -1, 602, 601, 603, -1, 600, 602, 601, -1, 604, 605, 600, -1, 605, 602, 600, -1, 600, 606, 602, -1, 608, 600, 609, -1, 609, 600, 602, -1, 602, 607, 606, -1, 610, 611, 612, -1, 614, 610, 615, -1, 615, 610, 612, -1, 612, 613, 611, -1, 618, 610, 619, -1, 610, 612, 616, -1, 612, 617, 616, -1, 619, 612, 610, -1, 620, 622, 621, -1, 622, 623, 621, -1, 624, 625, 620, -1, 625, 622, 620, -1, 629, 620, 622, -1, 620, 622, 626, -1, 622, 627, 626, -1, 628, 629, 620, -1, 630, 631, 632, -1, 634, 630, 635, -1, 635, 630, 632, -1, 632, 633, 631, -1, 638, 630, 639, -1, 630, 632, 636, -1, 632, 637, 636, -1, 639, 632, 630, -1, 642, 641, 643, -1, 640, 642, 641, -1, 644, 645, 640, -1, 645, 642, 640, -1, 640, 646, 642, -1, 648, 640, 649, -1, 649, 640, 642, -1, 642, 647, 646, -1, 650, 651, 652, -1, 652, 651, 653, -1, 655, 650, 652, -1, 654, 655, 650, -1, 652, 656, 657, -1, 658, 650, 659, -1, 659, 650, 652, -1, 650, 652, 656, -1, 660, 662, 661, -1, 662, 663, 661, -1, 664, 665, 660, -1, 665, 662, 660, -1, 660, 666, 662, -1, 668, 660, 669, -1, 662, 667, 666, -1, 669, 662, 660, -1, 674, 670, 675, -1, 670, 672, 671, -1, 672, 673, 671, -1, 675, 672, 670, -1, 670, 676, 672, -1, 672, 676, 677, -1, 678, 670, 679, -1, 679, 670, 672, -1, 682, 681, 683, -1, 684, 680, 685, -1, 680, 682, 681, -1, 685, 682, 680, -1, 682, 686, 687, -1, 688, 680, 689, -1, 680, 682, 686, -1, 689, 682, 680, -1, 694, 690, 695, -1, 690, 692, 691, -1, 692, 693, 691, -1, 695, 692, 690, -1, 690, 696, 692, -1, 692, 696, 697, -1, 698, 690, 699, -1, 699, 692, 690, -1, 702, 701, 703, -1, 704, 700, 705, -1, 705, 700, 702, -1, 700, 702, 701, -1, 700, 706, 702, -1, 702, 706, 707, -1, 708, 700, 709, -1, 709, 700, 702, -1, 710, 712, 711, -1, 712, 713, 711, -1, 714, 715, 710, -1, 715, 712, 710, -1, 719, 710, 712, -1, 710, 712, 716, -1, 712, 717, 716, -1, 718, 719, 710, -1, 720, 721, 722, -1, 722, 721, 723, -1, 725, 720, 722, -1, 724, 725, 720, -1, 720, 726, 722, -1, 722, 726, 727, -1, 729, 720, 722, -1, 728, 729, 720, -1, 734, 730, 735, -1, 735, 730, 732, -1, 730, 732, 731, -1, 732, 733, 731, -1, 730, 736, 732, -1, 738, 730, 739, -1, 739, 730, 732, -1, 732, 737, 736, -1, 742, 741, 743, -1, 745, 740, 742, -1, 740, 742, 741, -1, 744, 745, 740, -1, 740, 746, 742, -1, 749, 740, 742, -1, 742, 747, 746, -1, 748, 749, 740, -1, 750, 751, 752, -1, 752, 751, 753, -1, 755, 750, 752, -1, 754, 755, 750, -1, 750, 756, 752, -1, 752, 756, 757, -1, 759, 750, 752, -1, 758, 759, 750, -1, 764, 760, 765, -1, 765, 760, 762, -1, 760, 762, 761, -1, 762, 763, 761, -1, 760, 766, 762, -1, 768, 760, 769, -1, 769, 760, 762, -1, 762, 767, 766, -1, 770, 771, 772, -1, 772, 771, 773, -1, 775, 770, 772, -1, 774, 775, 770, -1, 770, 776, 772, -1, 772, 776, 777, -1, 779, 770, 772, -1, 778, 779, 770, -1, 780, 781, 782, -1, 782, 781, 783, -1, 784, 780, 785, -1, 785, 780, 782, -1, 782, 786, 787, -1, 780, 782, 786, -1, 788, 789, 780, -1, 789, 782, 780, -1, 794, 790, 795, -1, 795, 790, 792, -1, 790, 792, 791, -1, 792, 793, 791, -1, 790, 796, 792, -1, 798, 790, 799, -1, 799, 790, 792, -1, 792, 797, 796, -1, 800, 801, 802, -1, 802, 801, 803, -1, 805, 800, 802, -1, 804, 805, 800, -1, 800, 806, 802, -1, 802, 806, 807, -1, 809, 800, 802, -1, 808, 809, 800, -1, 810, 811, 812, -1, 815, 810, 812, -1, 812, 813, 811, -1, 814, 815, 810, -1, 810, 816, 812, -1, 819, 810, 812, -1, 812, 817, 816, -1, 818, 819, 810, -1, 820, 821, 822, -1, 822, 821, 823, -1, 825, 820, 822, -1, 824, 825, 820, -1, 829, 820, 822, -1, 820, 822, 826, -1, 822, 827, 826, -1, 828, 829, 820, -1, 830, 831, 832, -1, 835, 830, 832, -1, 832, 833, 831, -1, 834, 835, 830, -1, 830, 832, 836, -1, 832, 837, 836, -1, 838, 839, 830, -1, 839, 832, 830, -1, 840, 841, 842, -1, 844, 840, 845, -1, 845, 840, 842, -1, 842, 843, 841, -1, 842, 846, 847, -1, 848, 840, 849, -1, 840, 842, 846, -1, 849, 842, 840, -1, 852, 851, 853, -1, 850, 852, 851, -1, 854, 855, 850, -1, 855, 852, 850, -1, 859, 850, 852, -1, 850, 852, 856, -1, 852, 857, 856, -1, 858, 859, 850, -1, 860, 861, 862, -1, 865, 860, 862, -1, 862, 863, 861, -1, 864, 865, 860, -1, 860, 862, 866, -1, 862, 867, 866, -1, 868, 869, 860, -1, 869, 862, 860, -1, 870, 871, 872, -1, 872, 871, 873, -1, 874, 870, 875, -1, 875, 870, 872, -1, 872, 876, 877, -1, 870, 872, 876, -1, 878, 879, 870, -1, 879, 872, 870, -1, 880, 882, 881, -1, 882, 883, 881, -1, 884, 885, 880, -1, 885, 882, 880, -1, 889, 880, 882, -1, 880, 882, 886, -1, 882, 887, 886, -1, 888, 889, 880, -1, 890, 891, 892, -1, 894, 890, 895, -1, 895, 890, 892, -1, 892, 893, 891, -1, 898, 890, 899, -1, 890, 892, 896, -1, 892, 897, 896, -1, 899, 892, 890, -1, 900, 902, 901, -1, 902, 903, 901, -1, 904, 905, 900, -1, 905, 902, 900, -1, 909, 900, 902, -1, 900, 902, 906, -1, 902, 907, 906, -1, 908, 909, 900, -1, 910, 911, 912, -1, 914, 910, 915, -1, 915, 910, 912, -1, 912, 913, 911, -1, 918, 910, 919, -1, 910, 912, 916, -1, 912, 917, 916, -1, 919, 912, 910, -1, 922, 921, 923, -1, 920, 922, 921, -1, 924, 925, 920, -1, 925, 922, 920, -1, 920, 926, 922, -1, 928, 920, 929, -1, 929, 920, 922, -1, 922, 927, 926, -1
              ]
              texCoordIndex [
                0, 1, 2, -1, 3, 4, 5, -1, 6, 7, 8, -1, 9, 10, 11, -1, 12, 13, 14, -1, 15, 16, 17, -1, 18, 19, 20, -1, 21, 22, 23, -1, 24, 25, 26, -1, 27, 28, 29, -1, 30, 31, 32, -1, 33, 34, 35, -1, 36, 37, 38, -1, 39, 40, 41, -1, 42, 43, 44, -1, 45, 46, 47, -1, 48, 49, 50, -1, 51, 52, 53, -1, 54, 55, 56, -1, 57, 58, 59, -1, 60, 61, 62, -1, 63, 64, 65, -1, 66, 67, 68, -1, 69, 70, 71, -1, 72, 73, 74, -1, 75, 76, 77, -1, 78, 79, 80, -1, 81, 82, 83, -1, 84, 85, 86, -1, 87, 88, 89, -1, 90, 91, 92, -1, 93, 94, 95, -1, 96, 97, 98, -1, 99, 100, 101, -1, 102, 103, 104, -1, 105, 106, 107, -1, 108, 109, 110, -1, 111, 112, 113, -1, 114, 115, 116, -1, 117, 118, 119, -1, 120, 121, 122, -1, 123, 124, 125, -1, 126, 127, 128, -1, 129, 130, 131, -1, 132, 133, 134, -1, 135, 136, 137, -1, 138, 139, 140, -1, 141, 142, 143, -1, 144, 145, 146, -1, 147, 148, 149, -1, 150, 151, 152, -1, 153, 154, 155, -1, 156, 157, 158, -1, 159, 160, 161, -1, 162, 163, 164, -1, 165, 166, 167, -1, 168, 169, 170, -1, 171, 172, 173, -1, 174, 175, 176, -1, 177, 178, 179, -1, 180, 181, 182, -1, 183, 184, 185, -1, 186, 187, 188, -1, 189, 190, 191, -1, 192, 193, 194, -1, 195, 196, 197, -1, 198, 199, 200, -1, 201, 202, 203, -1, 204, 205, 206, -1, 207, 208, 209, -1, 210, 211, 212, -1, 213, 214, 215, -1, 216, 217, 218, -1, 219, 220, 221, -1, 222, 223, 224, -1, 225, 226, 227, -1, 228, 229, 230, -1, 231, 232, 233, -1, 234, 235, 236, -1, 237, 238, 239, -1, 240, 241, 242, -1, 243, 244, 245, -1, 246, 247, 248, -1, 249, 250, 251, -1, 252, 253, 254, -1, 255, 256, 257, -1, 258, 259, 260, -1, 261, 262, 263, -1, 264, 265, 266, -1, 267, 268, 269, -1, 270, 271, 272, -1, 273, 274, 275, -1, 276, 277, 278, -1, 279, 280, 281, -1, 282, 283, 284, -1, 285, 286, 287, -1, 288, 289, 290, -1, 291, 292, 293, -1, 294, 295, 296, -1, 297, 298, 299, -1, 300, 301, 302, -1, 303, 304, 305, -1, 306, 307, 308, -1, 309, 310, 311, -1, 312, 313, 314, -1, 315, 316, 317, -1, 318, 319, 320, -1, 321, 322, 323, -1, 324, 325, 326, -1, 327, 328, 329, -1, 330, 331, 332, -1, 333, 334, 335, -1, 336, 337, 338, -1, 339, 340, 341, -1, 342, 343, 344, -1, 345, 346, 347, -1, 348, 349, 350, -1, 351, 352, 353, -1, 354, 355, 356, -1, 357, 358, 359, -1, 360, 361, 362, -1, 363, 364, 365, -1, 366, 367, 368, -1, 369, 370, 371, -1, 372, 373, 374, -1, 375, 376, 377, -1, 378, 379, 380, -1, 381, 382, 383, -1, 384, 385, 386, -1, 387, 388, 389, -1, 390, 391, 392, -1, 393, 394, 395, -1, 396, 397, 398, -1, 399, 400, 401, -1, 402, 403, 404, -1, 405, 406, 407, -1, 408, 409, 410, -1, 411, 412, 413, -1, 414, 415, 416, -1, 417, 418, 419, -1, 420, 421, 422, -1, 423, 424, 425, -1, 426, 427, 428, -1, 429, 430, 431, -1, 432, 433, 434, -1, 435, 436, 437, -1, 438, 439, 440, -1, 441, 442, 443, -1, 444, 445, 446, -1, 447, 448, 449, -1, 450, 451, 452, -1, 453, 454, 455, -1, 456, 457, 458, -1, 459, 460, 461, -1, 462, 463, 464, -1, 465, 466, 467, -1, 468, 469, 470, -1, 471, 472, 473, -1, 474, 475, 476, -1, 477, 478, 479, -1, 480, 481, 482, -1, 483, 484, 485, -1, 486, 487, 488, -1, 489, 490, 491, -1, 492, 493, 494, -1, 495, 496, 497, -1, 498, 499, 500, -1, 501, 502, 503, -1, 504, 505, 506, -1, 507, 508, 509, -1, 510, 511, 512, -1, 513, 514, 515, -1, 516, 517, 518, -1, 519, 520, 521, -1, 522, 523, 524, -1, 525, 526, 527, -1, 528, 529, 530, -1, 531, 532, 533, -1, 534, 535, 536, -1, 537, 538, 539, -1, 540, 541, 542, -1, 543, 544, 545, -1, 546, 547, 548, -1, 549, 550, 551, -1, 552, 553, 554, -1, 555, 556, 557, -1, 558, 559, 560, -1, 561, 562, 563, -1, 564, 565, 566, -1, 567, 568, 569, -1, 570, 571, 572, -1, 573, 574, 575, -1, 576, 577, 578, -1, 579, 580, 581, -1, 582, 583, 584, -1, 585, 586, 587, -1, 588, 589, 590, -1, 591, 592, 593, -1, 594, 595, 596, -1, 597, 598, 599, -1, 600, 601, 602, -1, 603, 604, 605, -1, 606, 607, 608, -1, 609, 610, 611, -1, 612, 613, 614, -1, 615, 616, 617, -1, 618, 619, 620, -1, 621, 622, 623, -1, 624, 625, 626, -1, 627, 628, 629, -1, 630, 631, 632, -1, 633, 634, 635, -1, 636, 637, 638, -1, 639, 640, 641, -1, 642, 643, 644, -1, 645, 646, 647, -1, 648, 649, 650, -1, 651, 652, 653, -1, 654, 655, 656, -1, 657, 658, 659, -1, 660, 661, 662, -1, 663, 664, 665, -1, 666, 667, 668, -1, 669, 670, 671, -1, 672, 673, 674, -1, 675, 676, 677, -1, 678, 679, 680, -1, 681, 682, 683, -1, 684, 685, 686, -1, 687, 688, 689, -1, 690, 691, 692, -1, 693, 694, 695, -1, 696, 697, 698, -1, 699, 700, 701, -1, 702, 703, 704, -1, 705, 706, 707, -1, 708, 709, 710, -1, 711, 712, 713, -1, 714, 715, 716, -1, 717, 718, 719, -1, 720, 721, 722, -1, 723, 724, 725, -1, 726, 727, 728, -1, 729, 730, 731, -1, 732, 733, 734, -1, 735, 736, 737, -1, 738, 739, 740, -1, 741, 742, 743, -1, 744, 745, 746, -1, 747, 748, 749, -1, 750, 751, 752, -1, 753, 754, 755, -1, 756, 757, 758, -1, 759, 760, 761, -1, 762, 763, 764, -1, 765, 766, 767, -1, 768, 769, 770, -1, 771, 772, 773, -1, 774, 775, 776, -1, 777, 778, 779, -1, 780, 781, 782, -1, 783, 784, 785, -1, 786, 787, 788, -1, 789, 790, 791, -1, 792, 793, 794, -1, 795, 796, 797, -1, 798, 799, 800, -1, 801, 802, 803, -1, 804, 805, 806, -1, 807, 808, 809, -1, 810, 811, 812, -1, 813, 814, 815, -1, 816, 817, 818, -1, 819, 820, 821, -1, 822, 823, 824, -1, 825, 826, 827, -1, 828, 829, 830, -1, 831, 832, 833, -1, 834, 835, 836, -1, 837, 838, 839, -1, 840, 841, 842, -1, 843, 844, 845, -1, 846, 847, 848, -1, 849, 850, 851, -1, 852, 853, 854, -1, 855, 856, 857, -1, 858, 859, 860, -1, 861, 862, 863, -1, 864, 865, 866, -1, 867, 868, 869, -1, 870, 871, 872, -1, 873, 874, 875, -1, 876, 877, 878, -1, 879, 880, 881, -1, 882, 883, 884, -1, 885, 886, 887, -1, 888, 889, 890, -1, 891, 892, 893, -1, 894, 895, 896, -1, 897, 898, 899, -1, 900, 901, 902, -1, 903, 904, 905, -1, 906, 907, 908, -1, 909, 910, 911, -1, 912, 913, 914, -1, 915, 916, 917, -1, 918, 919, 920, -1, 921, 922, 923, -1, 924, 925, 926, -1, 927, 928, 929, -1, 930, 931, 932, -1, 933, 934, 935, -1, 936, 937, 938, -1, 939, 940, 941, -1, 942, 943, 944, -1, 945, 946, 947, -1, 948, 949, 950, -1, 951, 952, 953, -1, 954, 955, 956, -1, 957, 958, 959, -1, 960, 961, 962, -1, 963, 964, 965, -1, 966, 967, 968, -1, 969, 970, 971, -1, 972, 973, 974, -1, 975, 976, 977, -1, 978, 979, 980, -1, 981, 982, 983, -1, 984, 985, 986, -1, 987, 988, 989, -1, 990, 991, 992, -1, 993, 994, 995, -1, 996, 997, 998, -1, 999, 1000, 1001, -1, 1002, 1003, 1004, -1, 1005, 1006, 1007, -1, 1008, 1009, 1010, -1, 1011, 1012, 1013, -1, 1014, 1015, 1016, -1, 1017, 1018, 1019, -1, 1020, 1021, 1022, -1, 1023, 1024, 1025, -1, 1026, 1027, 1028, -1, 1029, 1030, 1031, -1, 1032, 1033, 1034, -1, 1035, 1036, 1037, -1, 1038, 1039, 1040, -1, 1041, 1042, 1043, -1, 1044, 1045, 1046, -1, 1047, 1048, 1049, -1, 1050, 1051, 1052, -1, 1053, 1054, 1055, -1, 1056, 1057, 1058, -1, 1059, 1060, 1061, -1, 1062, 1063, 1064, -1, 1065, 1066, 1067, -1, 1068, 1069, 1070, -1, 1071, 1072, 1073, -1, 1074, 1075, 1076, -1, 1077, 1078, 1079, -1, 1080, 1081, 1082, -1, 1083, 1084, 1085, -1, 1086, 1087, 1088, -1, 1089, 1090, 1091, -1, 1092, 1093, 1094, -1, 1095, 1096, 1097, -1, 1098, 1099, 1100, -1, 1101, 1102, 1103, -1, 1104, 1105, 1106, -1, 1107, 1108, 1109, -1, 1110, 1111, 1112, -1, 1113, 1114, 1115, -1, 1116, 1117, 1118, -1, 1119, 1120, 1121, -1, 1122, 1123, 1124, -1, 1125, 1126, 1127, -1, 1128, 1129, 1130, -1, 1131, 1132, 1133, -1, 1134, 1135, 1136, -1, 1137, 1138, 1139, -1, 1140, 1141, 1142, -1, 1143, 1144, 1145, -1, 1146, 1147, 1148, -1, 1149, 1150, 1151, -1, 1152, 1153, 1154, -1, 1155, 1156, 1157, -1, 1158, 1159, 1160, -1, 1161, 1162, 1163, -1, 1164, 1165, 1166, -1, 1167, 1168, 1169, -1, 1170, 1171, 1172, -1, 1173, 1174, 1175, -1, 1176, 1177, 1178, -1, 1179, 1180, 1181, -1, 1182, 1183, 1184, -1, 1185, 1186, 1187, -1, 1188, 1189, 1190, -1, 1191, 1192, 1193, -1, 1194, 1195, 1196, -1, 1197, 1198, 1199, -1, 1200, 1201, 1202, -1, 1203, 1204, 1205, -1, 1206, 1207, 1208, -1, 1209, 1210, 1211, -1, 1212, 1213, 1214, -1, 1215, 1216, 1217, -1, 1218, 1219, 1220, -1, 1221, 1222, 1223, -1, 1224, 1225, 1226, -1, 1227, 1228, 1229, -1, 1230, 1231, 1232, -1, 1233, 1234, 1235, -1, 1236, 1237, 1238, -1, 1239, 1240, 1241, -1, 1242, 1243, 1244, -1, 1245, 1246, 1247, -1, 1248, 1249, 1250, -1, 1251, 1252, 1253, -1, 1254, 1255, 1256, -1, 1257, 1258, 1259, -1, 1260, 1261, 1262, -1, 1263, 1264, 1265, -1, 1266, 1267, 1268, -1, 1269, 1270, 1271, -1, 1272, 1273, 1274, -1, 1275, 1276, 1277, -1, 1278, 1279, 1280, -1, 1281, 1282, 1283, -1, 1284, 1285, 1286, -1, 1287, 1288, 1289, -1, 1290, 1291, 1292, -1, 1293, 1294, 1295, -1, 1296, 1297, 1298, -1, 1299, 1300, 1301, -1, 1302, 1303, 1304, -1, 1305, 1306, 1307, -1, 1308, 1309, 1310, -1, 1311, 1312, 1313, -1, 1314, 1315, 1316, -1, 1317, 1318, 1319, -1, 1320, 1321, 1322, -1, 1323, 1324, 1325, -1, 1326, 1327, 1328, -1, 1329, 1330, 1331, -1, 1332, 1333, 1334, -1, 1335, 1336, 1337, -1, 1338, 1339, 1340, -1, 1341, 1342, 1343, -1, 1344, 1345, 1346, -1, 1347, 1348, 1349, -1, 1350, 1351, 1352, -1, 1353, 1354, 1355, -1, 1356, 1357, 1358, -1, 1359, 1360, 1361, -1, 1362, 1363, 1364, -1, 1365, 1366, 1367, -1, 1368, 1369, 1370, -1, 1371, 1372, 1373, -1, 1374, 1375, 1376, -1, 1377, 1378, 1379, -1, 1380, 1381, 1382, -1, 1383, 1384, 1385, -1, 1386, 1387, 1388, -1, 1389, 1390, 1391, -1, 1392, 1393, 1394, -1, 1395, 1396, 1397, -1, 1398, 1399, 1400, -1, 1401, 1402, 1403, -1, 1404, 1405, 1406, -1, 1407, 1408, 1409, -1, 1410, 1411, 1412, -1, 1413, 1414, 1415, -1, 1416, 1417, 1418, -1, 1419, 1420, 1421, -1, 1422, 1423, 1424, -1, 1425, 1426, 1427, -1, 1428, 1429, 1430, -1, 1431, 1432, 1433, -1, 1434, 1435, 1436, -1, 1437, 1438, 1439, -1, 1440, 1441, 1442, -1, 1443, 1444, 1445, -1, 1446, 1447, 1448, -1, 1449, 1450, 1451, -1, 1452, 1453, 1454, -1, 1455, 1456, 1457, -1, 1458, 1459, 1460, -1, 1461, 1462, 1463, -1, 1464, 1465, 1466, -1, 1467, 1468, 1469, -1, 1470, 1471, 1472, -1, 1473, 1474, 1475, -1, 1476, 1477, 1478, -1, 1479, 1480, 1481, -1, 1482, 1483, 1484, -1, 1485, 1486, 1487, -1, 1488, 1489, 1490, -1, 1491, 1492, 1493, -1, 1494, 1495, 1496, -1, 1497, 1498, 1499, -1, 1500, 1501, 1502, -1, 1503, 1504, 1505, -1, 1506, 1507, 1508, -1, 1509, 1510, 1511, -1, 1512, 1513, 1514, -1, 1515, 1516, 1517, -1, 1518, 1519, 1520, -1, 1521, 1522, 1523, -1, 1524, 1525, 1526, -1, 1527, 1528, 1529, -1, 1530, 1531, 1532, -1, 1533, 1534, 1535, -1, 1536, 1537, 1538, -1, 1539, 1540, 1541, -1, 1542, 1543, 1544, -1, 1545, 1546, 1547, -1, 1548, 1549, 1550, -1, 1551, 1552, 1553, -1, 1554, 1555, 1556, -1, 1557, 1558, 1559, -1, 1560, 1561, 1562, -1, 1563, 1564, 1565, -1, 1566, 1567, 1568, -1, 1569, 1570, 1571, -1, 1572, 1573, 1574, -1, 1575, 1576, 1577, -1, 1578, 1579, 1580, -1, 1581, 1582, 1583, -1, 1584, 1585, 1586, -1, 1587, 1588, 1589, -1, 1590, 1591, 1592, -1, 1593, 1594, 1595, -1, 1596, 1597, 1598, -1, 1599, 1600, 1601, -1, 1602, 1603, 1604, -1, 1605, 1606, 1607, -1, 1608, 1609, 1610, -1, 1611, 1612, 1613, -1, 1614, 1615, 1616, -1, 1617, 1618, 1619, -1, 1620, 1621, 1622, -1, 1623, 1624, 1625, -1, 1626, 1627, 1628, -1, 1629, 1630, 1631, -1, 1632, 1633, 1634, -1, 1635, 1636, 1637, -1, 1638, 1639, 1640, -1, 1641, 1642, 1643, -1, 1644, 1645, 1646, -1, 1647, 1648, 1649, -1, 1650, 1651, 1652, -1, 1653, 1654, 1655, -1, 1656, 1657, 1658, -1, 1659, 1660, 1661, -1, 1662, 1663, 1664, -1, 1665, 1666, 1667, -1, 1668, 1669, 1670, -1, 1671, 1672, 1673, -1, 1674, 1675, 1676, -1, 1677, 1678, 1679, -1, 1680, 1681, 1682, -1, 1683, 1684, 1685, -1, 1686, 1687, 1688, -1, 1689, 1690, 1691, -1, 1692, 1693, 1694, -1, 1695, 1696, 1697, -1, 1698, 1699, 1700, -1, 1701, 1702, 1703, -1, 1704, 1705, 1706, -1, 1707, 1708, 1709, -1, 1710, 1711, 1712, -1, 1713, 1714, 1715, -1, 1716, 1717, 1718, -1, 1719, 1720, 1721, -1, 1722, 1723, 1724, -1, 1725, 1726, 1727, -1, 1728, 1729, 1730, -1, 1731, 1732, 1733, -1, 1734, 1735, 1736, -1, 1737, 1738, 1739, -1, 1740, 1741, 1742, -1, 1743, 1744, 1745, -1, 1746, 1747, 1748, -1, 1749, 1750, 1751, -1, 1752, 1753, 1754, -1, 1755, 1756, 1757, -1, 1758, 1759, 1760, -1, 1761, 1762, 1763, -1, 1764, 1765, 1766, -1, 1767, 1768, 1769, -1, 1770, 1771, 1772, -1, 1773, 1774, 1775, -1, 1776, 1777, 1778, -1, 1779, 1780, 1781, -1, 1782, 1783, 1784, -1, 1785, 1786, 1787, -1, 1788, 1789, 1790, -1, 1791, 1792, 1793, -1, 1794, 1795, 1796, -1, 1797, 1798, 1799, -1, 1800, 1801, 1802, -1, 1803, 1804, 1805, -1, 1806, 1807, 1808, -1, 1809, 1810, 1811, -1, 1812, 1813, 1814, -1, 1815, 1816, 1817, -1, 1818, 1819, 1820, -1, 1821, 1822, 1823, -1, 1824, 1825, 1826, -1, 1827, 1828, 1829, -1, 1830, 1831, 1832, -1, 1833, 1834, 1835, -1, 1836, 1837, 1838, -1, 1839, 1840, 1841, -1, 1842, 1843, 1844, -1, 1845, 1846, 1847, -1, 1848, 1849, 1850, -1, 1851, 1852, 1853, -1, 1854, 1855, 1856, -1, 1857, 1858, 1859, -1, 1860, 1861, 1862, -1, 1863, 1864, 1865, -1, 1866, 1867, 1868, -1, 1869, 1870, 1871, -1, 1872, 1873, 1874, -1, 1875, 1876, 1877, -1, 1878, 1879, 1880, -1, 1881, 1882, 1883, -1, 1884, 1885, 1886, -1, 1887, 1888, 1889, -1, 1890, 1891, 1892, -1, 1893, 1894, 1895, -1, 1896, 1897, 1898, -1, 1899, 1900, 1901, -1, 1902, 1903, 1904, -1, 1905, 1906, 1907, -1, 1908, 1909, 1910, -1, 1911, 1912, 1913, -1, 1914, 1915, 1916, -1, 1917, 1918, 1919, -1, 1920, 1921, 1922, -1, 1923, 1924, 1925, -1, 1926, 1927, 1928, -1, 1929, 1930, 1931, -1, 1932, 1933, 1934, -1, 1935, 1936, 1937, -1, 1938, 1939, 1940, -1, 1941, 1942, 1943, -1, 1944, 1945, 1946, -1, 1947, 1948, 1949, -1, 1950, 1951, 1952, -1, 1953, 1954, 1955, -1, 1956, 1957, 1958, -1, 1959, 1960, 1961, -1, 1962, 1963, 1964, -1, 1965, 1966, 1967, -1, 1968, 1969, 1970, -1, 1971, 1972, 1973, -1, 1974, 1975, 1976, -1, 1977, 1978, 1979, -1, 1980, 1981, 1982, -1, 1983, 1984, 1985, -1, 1986, 1987, 1988, -1, 1989, 1990, 1991, -1, 1992, 1993, 1994, -1, 1995, 1996, 1997, -1, 1998, 1999, 2000, -1, 2001, 2002, 2003, -1, 2004, 2005, 2006, -1, 2007, 2008, 2009, -1, 2010, 2011, 2012, -1, 2013, 2014, 2015, -1, 2016, 2017, 2018, -1, 2019, 2020, 2021, -1, 2022, 2023, 2024, -1, 2025, 2026, 2027, -1, 2028, 2029, 2030, -1, 2031, 2032, 2033, -1, 2034, 2035, 2036, -1, 2037, 2038, 2039, -1, 2040, 2041, 2042, -1, 2043, 2044, 2045, -1, 2046, 2047, 2048, -1, 2049, 2050, 2051, -1, 2052, 2053, 2054, -1, 2055, 2056, 2057, -1, 2058, 2059, 2060, -1, 2061, 2062, 2063, -1, 2064, 2065, 2066, -1, 2067, 2068, 2069, -1, 2070, 2071, 2072, -1, 2073, 2074, 2075, -1, 2076, 2077, 2078, -1, 2079, 2080, 2081, -1, 2082, 2083, 2084, -1, 2085, 2086, 2087, -1, 2088, 2089, 2090, -1, 2091, 2092, 2093, -1, 2094, 2095, 2096, -1, 2097, 2098, 2099, -1, 2100, 2101, 2102, -1, 2103, 2104, 2105, -1, 2106, 2107, 2108, -1, 2109, 2110, 2111, -1, 2112, 2113, 2114, -1, 2115, 2116, 2117, -1, 2118, 2119, 2120, -1, 2121, 2122, 2123, -1, 2124, 2125, 2126, -1, 2127, 2128, 2129, -1, 2130, 2131, 2132, -1, 2133, 2134, 2135, -1, 2136, 2137, 2138, -1, 2139, 2140, 2141, -1, 2142, 2143, 2144, -1, 2145, 2146, 2147, -1, 2148, 2149, 2150, -1, 2151, 2152, 2153, -1, 2154, 2155, 2156, -1, 2157, 2158, 2159, -1, 2160, 2161, 2162, -1, 2163, 2164, 2165, -1, 2166, 2167, 2168, -1, 2169, 2170, 2171, -1, 2172, 2173, 2174, -1, 2175, 2176, 2177, -1, 2178, 2179, 2180, -1, 2181, 2182, 2183, -1, 2184, 2185, 2186, -1, 2187, 2188, 2189, -1, 2190, 2191, 2192, -1, 2193, 2194, 2195, -1, 2196, 2197, 2198, -1, 2199, 2200, 2201, -1, 2202, 2203, 2204, -1, 2205, 2206, 2207, -1, 2208, 2209, 2210, -1, 2211, 2212, 2213, -1, 2214, 2215, 2216, -1, 2217, 2218, 2219, -1, 2220, 2221, 2222, -1, 2223, 2224, 2225, -1, 2226, 2227, 2228, -1, 2229, 2230, 2231, -1
              ]
            }
          }
        ]
      }
    ]
    %< if (fields.enableBoundingObject.value) { >%
    boundingObject Pose {
      translation 0 0 %<= fields.scale.value * 2.125 >%
      children [
        Cylinder {
          height %<= fields.scale.value * 4.25 >%
          radius %<= fields.scale.value * 0.33 >%
        }
      ]
    }
    %< } >%
  }
}
