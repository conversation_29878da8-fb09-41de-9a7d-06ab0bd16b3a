#VRML_SIM R2023b utf8
PROTO Tunnel [
  # 原有参数
  field SFVec3f    translation 0 0 0
  field SFRotation rotation    0 1 0 0
  field SFFloat    radius      5
  field SFFloat    thickness   1
  field SFFloat    length      50      # X方向长度
  field SFFloat    startAngle  85
  field SFFloat    endAngle    275
  field SFFloat    Segments    150
  field SFColor    outerWallColor 0.3 0.3 0.3
  field SFColor    innerWallColor 0.5 0.5 0.5
  field SFColor    topRingColor    0.7 0.7 0.7    # Y+方向
  field SFColor    bottomRingColor 0.7 0.7 0.7   # Y-方向
  field SFFloat    roughness   0.7
  field SFFloat    metalness   0.1
  field SFBool     enableInnerWall TRUE
  field SFBool     enableTopRing    TRUE
  field SFBool     enableBottomRing TRUE
  field SFFloat    innerWallTransparency 0
  field SFBool     locked FALSE

  # 新增：顶部两排灯参数
  field SFBool     enableTopLights TRUE
  field SFColor    topLightColor 1 1 1          # 顶部灯颜色

  field SFFloat    topLightOffset 2.0           # 灯组距离中轴线的偏移（Y轴）
  field SFInt32    topLightsPerSide 10          # 每侧灯的数量
  field SFFloat    topLightHeight 4.5           # 灯的安装高度（Z轴） 
  field SFFloat    topLightLeftIntensity  10    # 左边灯照射强度
  field SFFloat    topLightLeftbeamWidth  1.2    # 左边灯照射角度
  field SFFloat    topLightLeftcutOffAngle  1.7    # 左边灯照射角度
  field SFFloat    topLightRightIntensity 10    # 右边灯照射强度   
  field SFFloat    topLightRightbeamWidth  1.2    # 右边灯照射角度
  field SFFloat    topLightRightcutOffAngle  1.7    # 右边灯照射角度
  

]
{
  %{
    local innerRadius = fields.radius.value - fields.thickness.value
    if innerRadius < 0 then
      io.stderr:write("Warning: Thickness exceeds radius! Setting innerRadius to 0.\n")
      innerRadius = 0
    end

    local startAngleRad = math.rad(fields.startAngle.value)
    local endAngleRad = math.rad(fields.endAngle.value)
    local angleRange = endAngleRad - startAngleRad
    if angleRange <= 0 then angleRange = angleRange + 2 * math.pi end

    local fullSegments = fields.Segments.value
    local visibleSegments = math.max(3, math.floor(fullSegments * angleRange / (2 * math.pi) + 0.5))
    io.stderr:write("Visible segments: " .. visibleSegments .. "\n")
  }%

  Solid {
    translation IS translation
    rotation IS rotation
    locked IS locked
    children [
      # 主隧道结构（保持原有代码不变）
      Transform {
        rotation 0 1 0 1.5708  # 旋转使Z轴指向X轴
        children [
           # 外圆柱侧面
          Shape {
            appearance PBRAppearance {
              baseColor IS outerWallColor
              roughness IS roughness
              metalness IS metalness
            }
            geometry IndexedFaceSet {
              coord Coordinate {
                point [
                  %{ for i = 0, visibleSegments do
                      local angle = startAngleRad + (i / visibleSegments) * angleRange
                      local x = fields.radius.value * math.cos(angle)
                      local y = fields.radius.value * math.sin(angle)
                  }%
                    %{= x }% %{= y }% %{= fields.length.value / 2 }%
                    %{= x }% %{= y }% %{= -fields.length.value / 2 }%
                  %{ end }%
                ]
              }
              coordIndex [
                %{ for i = 0, visibleSegments - 1 do }%
                  %{= 2*i }% %{= 2*i+1 }% %{= 2*(i+1)+1 }% -1
                  %{= 2*(i+1)+1 }% %{= 2*(i+1) }% %{= 2*i }% -1
                %{ end }%
              ]
            }
          }

          # 内圆柱侧面（可选）
          %{ if innerRadius > 0 and fields.enableInnerWall.value then }%
          Shape {
            appearance PBRAppearance {
              baseColor IS innerWallColor
              roughness IS roughness
              metalness IS metalness
              transparency IS innerWallTransparency
            }
            geometry IndexedFaceSet {
              coord Coordinate {
                point [
                  %{ for i = 0, visibleSegments do
                      local angle = startAngleRad + (i / visibleSegments) * angleRange
                      local x = innerRadius * math.cos(angle)
                      local y = innerRadius * math.sin(angle)
                  }%
                    %{= x }% %{= y }% %{= fields.length.value / 2 }%
                    %{= x }% %{= y }% %{= -fields.length.value / 2 }%
                  %{ end }%
                ]
              }
              coordIndex [
                %{ for i = 0, visibleSegments - 1 do }%
                  %{= 2*i }% %{= 2*(i+1) }% %{= 2*i+1 }% -1
                  %{= 2*i+1 }% %{= 2*(i+1) }% %{= 2*(i+1)+1 }% -1
                %{ end }%
              ]
            }
          }
          %{ end }%

           # 顶部圆环（现在是Y+方向）
          %{ if fields.enableTopRing.value and innerRadius > 0 then }%
          Shape {
            appearance PBRAppearance { baseColor IS topRingColor }
            geometry IndexedFaceSet {
              coord Coordinate {
                point [
                  %{ for i = 0, visibleSegments do
                      local angle = startAngleRad + (i / visibleSegments) * angleRange
                  }%
                    %{= fields.radius.value * math.cos(angle) }% %{= fields.radius.value * math.sin(angle) }% %{= fields.length.value / 2 }%
                  %{ end }%
                  %{ for i = 0, visibleSegments do
                      local angle = startAngleRad + (i / visibleSegments) * angleRange
                  }%
                    %{= innerRadius * math.cos(angle) }% %{= innerRadius * math.sin(angle) }% %{= fields.length.value / 2 }%
                  %{ end }%
                ]
              }
              coordIndex [
                %{ for i = 0, visibleSegments - 2 do }%
                  %{= i }% %{= i+1 }% %{= i+visibleSegments+1 }% -1
                  %{= i+visibleSegments+1 }% %{= i+visibleSegments }% %{= i }% -1
                %{ end }%
                %{= visibleSegments-1 }% %{= 0 }% %{= visibleSegments }% -1
                %{= visibleSegments }% %{= 2*visibleSegments-1 }% %{= visibleSegments-1 }% -1
              ]
            }
          }
          %{ end }%

          # 底部圆环（现在是Y-方向）
          %{ if fields.enableBottomRing.value and innerRadius > 0 then }%
          Shape {
            appearance PBRAppearance { baseColor IS bottomRingColor }
            geometry IndexedFaceSet {
              coord Coordinate {
                point [
                  %{ for i = 0, visibleSegments do
                      local angle = startAngleRad + (i / visibleSegments) * angleRange
                  }%
                    %{= fields.radius.value * math.cos(angle) }% %{= fields.radius.value * math.sin(angle) }% %{= -fields.length.value / 2 }%
                  %{ end }%
                  %{ for i = 0, visibleSegments do
                      local angle = startAngleRad + (i / visibleSegments) * angleRange
                  }%
                    %{= innerRadius * math.cos(angle) }% %{= innerRadius * math.sin(angle) }% %{= -fields.length.value / 2 }%
                  %{ end }%
                ]
              }
              coordIndex [
                %{ for i = 0, visibleSegments - 2 do }%
                  %{= i+visibleSegments }% %{= i+visibleSegments+1 }% %{= i+1 }% -1
                  %{= i+1 }% %{= i }% %{= i+visibleSegments }% -1
                %{ end }%
                %{= 2*visibleSegments-1 }% %{= visibleSegments }% %{= visibleSegments-1 }% -1
                %{= visibleSegments-1 }% %{= 0 }% %{= 2*visibleSegments-1 }% -1
              ]
            }
          }
          %{ end }%
        ]
      }

	# 新增：顶部两排灯（左右各一排）
	%{ if fields.enableTopLights.value then }%
	  Transform {
	    translation 0 %{= fields.topLightOffset.value }% %{= fields.topLightHeight.value }%
	    children [
	      %{ for i = 0, fields.topLightsPerSide.value - 1 do }%
		Transform {
		  translation %{= (-fields.length.value / 2) + (i / (fields.topLightsPerSide.value - 1)) * fields.length.value }% 0 0
		  children [
		    SpotLight {
		      attenuation 0 0 1  # 无距离衰减
		      color  IS topLightColor
		      intensity IS topLightLeftIntensity
		      beamWidth IS topLightLeftbeamWidth      # 照射角度（弧度，约45度）
		      cutOffAngle IS topLightLeftcutOffAngle    # 截止角度（弧度，约57度）
		      direction 0 0 -1   # 朝下照射
		      location 0 0 0     # 灯光位置（相对于当前 Transform）
		      radius 100.0        # 照射范围（增大）
		    }
		    # 可选：可视化灯光位置（小球）
		    Shape {
		      appearance PBRAppearance { baseColor IS topLightColor metalness 0.5 }
		      geometry Sphere { radius 0.5 }  # 增大灯光可视化大小
		    }
		  ]
		}
	      %{ end }%
	    ]
	  }
	  Transform {
	    translation 0 %{= -fields.topLightOffset.value }% %{= fields.topLightHeight.value }%
	    children [
	      %{ for i = 0, fields.topLightsPerSide.value - 1 do }%
		Transform {
		  translation %{= (-fields.length.value / 2) + (i / (fields.topLightsPerSide.value - 1)) * fields.length.value }% 0 0
	         children [
		    SpotLight {
		      attenuation 0 0 1  # 无距离衰减
		      color  IS topLightColor
		      intensity IS topLightRightIntensity
		      beamWidth IS topLightRightbeamWidth      # 照射角度（弧度，约45度）
		      cutOffAngle IS topLightRightcutOffAngle    # 截止角度（弧度，约57度）
		      direction 0 0 -1   # 朝下照射
		      location 0 0 0     # 灯光位置（相对于当前 Transform）
		      radius 100.0        # 照射范围（增大）
		    }
		    # 可选：可视化灯光位置（小球）
		    Shape {
		      appearance PBRAppearance { baseColor IS topLightColor metalness 0.5 }
		      geometry Sphere { radius 0.5 }  # 增大灯光可视化大小
		    }
		  ]
		}
	      %{ end }%
	    ]
	  }
	%{ end }%
    ]
    
    # 碰撞体（调整为X轴方向）
    %{ if true then }%
    boundingObject Group {
      children [
        Transform {
          rotation 0 1 0 1.5708
          children [
            Shape { 
              geometry Cylinder { 
                radius IS radius 
                height IS length 
                subdivision %{= visibleSegments }% 
              } 
            }
          ]
        }
        %{ if innerRadius > 0 and fields.enableInnerWall.value then }%
        Transform {
          rotation 0 1 0 1.5708
          children [
            Shape { 
              geometry Cylinder { 
                radius %{= innerRadius }% 
                height %{= fields.length.value + 0.1 }% 
                subdivision %{= visibleSegments }% 
              } 
            }
          ]
        }
        %{ end }%
      ]
    }
    %{ end }%
  }
}
