#VRML_SIM R2023b utf8

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackgroundLight.proto"
EXTERNPROTO "../protos/chitu.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/default/protos/SumoInterface.proto"
IMPORTABLE EXTERNPROTO "../protos/DBOXUltrasonicSensor.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/mercedes_benz/MercedesBenzSprinterSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/bmw/BmwX5Simple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/tesla/TeslaModel3Simple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/BusSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/citroen/CitroenCZeroSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/lincoln/LincolnMKZSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/toyota/ToyotaPriusSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckTankSimple.proto"

WorldInfo {
  ERP 0.6
  basicTimeStep 10
  lineScale 1
  contactProperties [
    ContactProperties {
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "CitroenCZeroWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "ToyotaPriusWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "LincolnMKZWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "RangeRoverSportSVRWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "TruckWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
  ]
}
Viewpoint {
  orientation -0.15794180173276798 0.3781181760283937 0.9121847577231095 1.4114562056434672
  position -118.0902154968419 -99.59494839242302 36.27482296604312
  near 1
  follow "vehicle"
  lensFlare LensFlare {
  }
}
SumoInterface {
  gui FALSE
}
TexturedBackground {
}
TexturedBackgroundLight {
}
Fog {
  color 0.3607843137254902 0.3607843137254902 0.3607843137254902
  visibilityRange 1200
}
DEF GROUND_SHAPE Solid {
  translation 0 0 -0.02
  children [
    Shape {
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/worlds/textures/ground.jpg"
          ]
        }
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 500 500
        }
      }
      geometry DEF GROUND_PLANE Plane {
        size 2000 2000
      }
    }
  ]
  name "solid(1)"
  boundingObject USE GROUND_PLANE
  locked TRUE
}
DEF WEBOTS_VEHICLE0 chitu {
  hidden position_0_0 4.551432168760213e-12
  hidden position2_0_0 16.546903864343726
  hidden position_0_1 -1.562527235484127e-11
  hidden position2_0_1 15.462928216813077
  hidden position_0_2 16.079890326135242
  hidden position_0_3 14.735427115611987
  hidden linearVelocity_0 -7.844231373515722e-10 -3.295694023957525e-08 -3.8520285472767156e-07
  hidden angularVelocity_0 1.1206300010949205e-07 -2.6687821616589137e-09 -3.9971719008547774e-13
  hidden rotation_1 -2.275716084380107e-12 -1 1.0148268178043775e-12 3.980533249984553
  hidden linearVelocity_1 -8.982206828016965e-10 -3.773749957042572e-08 -1.2598278741196203e-08
  hidden angularVelocity_1 1.1508081119021544e-07 -2.7401449741628485e-09 -3.944700859875924e-13
  hidden rotation_2 -7.812636177420635e-12 1 9.620030728035213e-13 2.8965576024539046
  hidden linearVelocity_2 -8.984419818888181e-10 -3.773696386470492e-08 -1.2598912175334605e-08
  hidden angularVelocity_2 1.1508053108202682e-07 -2.7401529299701437e-09 -3.993085402526565e-13
  hidden rotation_3 0 -1 0 3.5135197117760693
  hidden linearVelocity_3 -8.953900291035634e-10 -3.764369705484009e-08 -6.556654162789632e-09
  hidden angularVelocity_3 1.3356078070798265e-07 -3.1772594908757765e-09 -4.040123369148822e-13
  hidden rotation_4 0 0.9999999999999999 0 2.1690565012528142
  hidden linearVelocity_4 -8.953152612229495e-10 -3.7643173909085556e-08 -6.557287192862701e-09
  hidden angularVelocity_4 1.335586891570794e-07 -3.177204850902392e-09 -4.0792461462460323e-13
  translation -120.64293169411431 -18.979888751620578 0.2371060609920708
  rotation -0.0004860586950949185 -0.000474792618145334 -0.9999997691594306 1.5944427097546598
  controllerArgs [
    "webots_chitu_gt.yaml"
  ]
}
DEF OBJECT_PROCESSOR Robot {
  controller "object_process"
  supervisor TRUE
}
DEF BOX25 Solid {
  translation -121.169395 -53.110329 0
  rotation 0 0 1 -6.2e-05
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX25"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX24 Solid {
  translation -118.993698 -3.796453 0
  rotation 0 0 1 -1.3e-05
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX24"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX21 Solid {
  translation -66.359467 -2.083474 0
  rotation 0 0 1 -1e-06
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX21"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX20 Solid {
  translation -63.991314 1.832666 0
  rotation 0 0 1 -4e-06
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX20"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX22 Solid {
  translation -49.809471 4.555016 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX22"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX19 Solid {
  translation -58.812126 1.272092 0
  rotation 0 0 1 -1e-06
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX19"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX18 Solid {
  translation -13.206577 -11.406483 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX18"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX4 Solid {
  translation -74.495201 -67.407692 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX4"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX5 Solid {
  translation -74.820374 -63.534607 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX5"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX6 Solid {
  translation -73.937225 -71.181969 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX6"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX7 Solid {
  translation -45.930054 -67.124374 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX7"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX8 Solid {
  translation -41.716949 -63.788139 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX8"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX15 Solid {
  translation -13.446992 -39.612003 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX15"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX23 Solid {
  translation -117.279999 3.46738 0
  rotation 0 0 1 -3.5e-05
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX23"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX9 Solid {
  translation -41.654266 -71.163864 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX9"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX11 Solid {
  translation -14.210041 -65.422859 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX11"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX28 Solid {
  translation -120.717056 -49.574318 0
  rotation 0 0 1 2.857673
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 3 1 1
      }
    }
  ]
  name "BOX28"
  boundingObject Box {
    size 3 1 1
  }
}
DEF BOX27 Solid {
  translation -125.592995 -51.130005 0
  rotation 0 0 1 2.857719
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 3 1 1
      }
    }
  ]
  name "BOX27"
  boundingObject Box {
    size 3 1 1
  }
}
DEF BOX12 Solid {
  translation -11.881325 -67.662323 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX12"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX17 Solid {
  translation -16.158518 -3.843805 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX17"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX13 Solid {
  translation -15.257792 -65.43235 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX13"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX14 Solid {
  translation -9.318419 -35.945976 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX14"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX10 Solid {
  translation -18.662323 -62.452652 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX10"
  boundingObject Box {
    size 1 1 1
  }
}
DEF BOX16 Solid {
  translation -11.292695 -0.076363 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 1 1 1
      }
    }
  ]
  name "BOX16"
  boundingObject Box {
    size 1 1 1
  }
}
DEF DBOX1 Robot {
  translation -119.85297298316392 -36.22859271407532 0
  rotation 0 0 -1 1.1244573144247634
  children [
    DBOXUltrasonicSensor {
      translation 2.5 0 0.5
    }
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 0 0 1
        }
      }
      geometry Box {
        size 5 2 2
      }
    }
  ]
  name "DBOX1"
  boundingObject Box {
    size 5 2 2
  }
  controller "path_motion"
  controllerArgs [
    "--type=line"
    "--velocity=1.000000"
    "--points=-122.267288,-29.531876;-119.846298,-36.541534;-116.950554,-40.571304"
  ]
  supervisor TRUE
  linearVelocity 0.43369082388224456 -0.9010598144951842 0
  angularVelocity 0 0 0.0827766374464256
}
