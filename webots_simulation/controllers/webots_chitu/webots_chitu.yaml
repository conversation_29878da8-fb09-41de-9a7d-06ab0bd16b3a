sensor_list: [<PERSON>, <PERSON><PERSON>,<PERSON><PERSON>_left, <PERSON><PERSON>_right, I<PERSON>, Object, Camera]

Car:
  type: Car
  odom_out_topic: "odom"
  ackermann_odom_out_topic: "ackermann/odom"
  twist_in_topic: "cmd_vel"
  ackermann_twist_in_topic: "uslam/control/ackermann_cmd_vel"

Lidar:
  type: Lidar
  wb_lidar_name: "laser_top"
  topic_name: "/sensor/lslidar_point_cloud/top/raw"
  link_name: "laser_link_top"
  filter_by_angle: false
  angle_range: 1.74533
Lidar_left:
  type: Lidar
  wb_lidar_name: "laser_left"
  topic_name: "/sensor/lslidar_point_cloud/left/raw"
  link_name: "laser_link_left"
Lidar_right:
  type: Lidar
  wb_lidar_name: "laser_right"
  topic_name: "/sensor/lslidar_point_cloud/right/raw"
  link_name: "laser_link_right"

UltrasonicSensor:
  type: UltrasonicSensor
  wb_ultrasonic_name: "UltrasonicSensor-12"
  topic_name: "/sensor/ultrasonic"
  link_name: "DistanceSensor_01"

IMU:
  type: I<PERSON>
  wb_acc_name: "accelerometer"
  wb_gyro_name: "gyro"
  wb_iu_name: "inertial unit"
  topic_name: "/sensor/imu"
  link_name: "imu_link"

Camera:
  type: CAMERA
  wb_camera_name: "camera_0"
  topic_name: "/sensor/camera_0"
  original_topic_name: "/sensor/camera_0/ori"
  link_name: "camera_link_top_left"
  obtain_drivable_space_msg: true
  drivable_space_topic_name: "/uslam/perception/perception_free_space"
  drivable_space_image_topic_name: "/webots_drivable_space_image_topic_"
  obtain_road_line_msg: false
  road_line_topic_name: "multi_task_lane_topic_"

RoadLine:
  type: RoadLine
  topic_name: "/sensor/road_line"
  link_name: "map"
  lane_map_dir: "/home/<USER>/workspace/uautopilot_simulation/lane_map_small.pcd"

Object:
  type: GT
  base_robot_def: "WEBOTS_VEHICLE0"
  target_def_prefix: "SUMO_VEHICLE"
  target_def_prefix_list: ["SUMO_VEHICLE", "BOX"]
  topic_name: "/uslam/perception/perception_objects"
  link_name: base_link
  car_ground_truth: "/car/ground_truth"
  use_ground_truth_tf: false
  use_lidar_ground_truth: true
  x_min: -30.0
  x_max: 30.0
  y_min: -30.0
  y_max: 30.0
  skip_cnt: 10

TL:
  type: TrafficLight
  base_robot_def: "WEBOTS_VEHICLE0"
  target_def_prefix: "TRAFFIC_"
  topic_name: "multi_task_objects_topic_"

AddObstacle:
  type: ADD_OBSTACLE
