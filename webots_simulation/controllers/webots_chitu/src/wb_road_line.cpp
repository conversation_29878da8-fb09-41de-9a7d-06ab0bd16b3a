#include "wb_road_line.hpp"

#include <pcl_conversions/pcl_conversions.h>
#include <ulog/ulog.h>

using namespace webots;

#include <cmath>

DDSWbRoadLine::DDSWbRoadLine(webots::Robot *robot, int step, const UParam &param)
    : DDSWbSensor("road_line", robot, step, param)
{
    supervisor_ = (webots::Supervisor *)robot;
}

bool DDSWbRoadLine::onInit()
{
    param_.param("topic_name", topic_name_, std::string("/road_lines/pointcloud"));
    param_.param("link_name", link_name_, std::string("world"));
    param_.param("lane_map_dir", lane_map_dir_, std::string("/home/<USER>/workspace/uautopilot_simulation/lane_map.pcd"));

    pc_writer_ = std::make_shared<uslam::transport::Writer<sensor_msgs::msg::PointCloud2>>(topic_name_);

    // gather road line nodes once
    if (!supervisor_) {
        printf("DDSWbRoadLine::onInit - ERROR: Could not find supervisor_");
        return false;
    } else {
        const Node *root = supervisor_->getRoot();
        if (root) findRoadSegments(root);
        pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZINormal>());
        ULOGW("Start extracting lane lines");
        for (const auto *seg : cached_road_segment_nodes_) {
            extractLaneLinesInSegment(seg, cloud);
        }

        for (const auto *seg : cached_curved_segment_nodes_) {
            extractLaneCurvedInSegment(seg, cloud);  // 和extractLaneLinesInSegment类似提取弯道车道线
        }
        ULOGW("Extracted %zu points from road segments", cloud->size());
        // 保存cloud点云为pcd
        pcl::io::savePCDFileBinary(lane_map_dir_, *cloud);
        ULOGW("Finished saving PCD file");
    }
    return true;
}

// 提取直道段的车道线，中心线为double，两侧为dashed，中心dashed线不生成
void DDSWbRoadLine::extractLaneLinesInSegment(const webots::Node *segment_node,
                                              pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud) {
    if (!segment_node) return;

    // === 读取必要字段 ===
    const Field *transField  = segment_node->getField("translation");
    const Field *rotField    = segment_node->getField("rotation");
    const Field *lengthField = segment_node->getField("length");
    const Field *widthField  = segment_node->getField("width");
    const Field *lanesField  = segment_node->getField("numberOfLanes");

    if (!transField || !rotField || !lengthField || !widthField || !lanesField) return;

    const double *t = transField->getSFVec3f();
    const double *r = rotField->getSFRotation();
    double length   = lengthField->getSFFloat();
    double width    = widthField->getSFFloat();
    int numberOfLanes = lanesField->getSFInt32();

    if (!t || !r || numberOfLanes < 1) return;

    int lanes = std::max(1, numberOfLanes);
    double lane_width = width / lanes;

    // === 姿态角 ===
    const double yaw = r[2] * r[3];
    const double cos_y = std::cos(yaw);
    const double sin_y = std::sin(yaw);

    // === 直道参数 ===
    const double dash_len_m = 2.0;   // 虚线段长度 (m)
    const double gap_len_m  = 2.0;   // 虚线间隔 (m)
    const double period_m   = dash_len_m + gap_len_m;
    const double sample_ds  = 0.05;  // 采样步长 (m)

    int sampleN = std::max(2, static_cast<int>(std::ceil(length / sample_ds)));

    // === 点投影函数 ===
    auto emitPoint = [&](double x_local, double y_local, int type_idx) {
        pcl::PointXYZINormal pt;
        pt.x = t[0] + cos_y * x_local - sin_y * y_local;
        pt.y = t[1] + sin_y * x_local + cos_y * y_local;
        pt.z = t[2];
        SemanticType semantic_type = semantic_label.find(static_cast<uint16_t>(type_idx)) == semantic_label.end()
                                         ? SemanticType::NONE
                                         : semantic_label.at(static_cast<uint16_t>(type_idx));
        switch (semantic_type) {
            case SemanticType::LANE:
                pt.intensity = 1.0f;
                break;
            case SemanticType::ROAD_MARKER:
                pt.intensity = 2.0f;
                break;
            case SemanticType::ROAD_SIGN:
                pt.intensity = 3.0f;
                break;
            case SemanticType::CROSSING:
                pt.intensity = 4.0f;
                break;
            case SemanticType::STOPLINE:
                pt.intensity = 5.0f;
                break;
            case SemanticType::POLE:
                pt.intensity = 6.0f;
                break;
            case SemanticType::SURF:
                pt.intensity = 7.0f;
                break;
            case SemanticType::GROUND:
                pt.intensity = 8.0f;
                break;
            default:
                ULOGW("Unknown semantic type");
                break;
        }
        pt.normal_y = type_idx;
        cloud->push_back(pt);
    };

    // === 遍历所有车道线 ===
    for (int b = 0; b <= lanes; ++b) {
        if(b == 0 || b == lanes) continue;
        double offset = -width / 2.0 + b * lane_width;  // 以道路中心为 0 偏移
        bool is_center = std::abs(offset) < 1e-6;
        bool is_center_dashed = (lanes % 2 == 0) && (b == lanes / 2);

        if (is_center) {
            // --- 中心双实线 ---
            const double d = 0.20;      // 双实线间距 (m)
            const double half_d = d * 0.5;

            for (int side = -1; side <= 1; side += 2) {
                double offset_line = offset + side * half_d;
                for (int i = 0; i <= sampleN; ++i) {
                    double s = (static_cast<double>(i) / sampleN) * length;
                    emitPoint(s, offset_line, 126);
                }
            }
        } else {
            if (is_center_dashed) continue; // 中心虚线不绘制

            // --- 虚线 ---
            for (int i = 0; i <= sampleN; ++i) {
                double s = (static_cast<double>(i) / sampleN) * length;

                // 按弧长位置计算相位，保证 dash 长度一致
                double phase = std::fmod(s, period_m);
                if (phase < dash_len_m) {
                    emitPoint(s, offset, 125);
                }
            }
        }
    }
}

// 弯道段车道线：中心线为 double，两侧为 dashed；中心 dashed 不生成
void DDSWbRoadLine::extractLaneCurvedInSegment(const webots::Node *segment_node,
                                               pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud) {
    if (!segment_node) return;

    // 读取字段
    const Field *transField  = segment_node->getField("translation");
    const Field *rotField    = segment_node->getField("rotation");
    const Field *widthField  = segment_node->getField("width");
    const Field *lanesField  = segment_node->getField("numberOfLanes");
    const Field *radiusField = segment_node->getField("curvatureRadius");
    const Field *angleField  = segment_node->getField("totalAngle");
    const Field *subdivField = segment_node->getField("subdivision");
    if (!transField || !rotField || !widthField || !lanesField || !radiusField || !angleField) return;

    const double *t = transField->getSFVec3f();
    const double *r = rotField->getSFRotation();
    if (!t || !r) return;

    const double width      = widthField->getSFFloat();
    const int    lanes_cnt  = std::max(1, lanesField->getSFInt32());
    const double R_center   = radiusField->getSFFloat();       // > 0
    const double totalAngle = angleField->getSFFloat();        // 可正可负
    int subdivision         = subdivField ? subdivField->getSFInt32() : 16;
    if (subdivision < 1) subdivision = 16;

    const double lane_width = width / lanes_cnt;

    const double yaw = r[2] * r[3];
    const double cos_y = std::cos(yaw);
    const double sin_y = std::sin(yaw);

    // dashed 参数（单位：米）
    const double dash_len_m = 2.0;
    const double gap_len_m  = 2.0;
    const double period_m   = dash_len_m + gap_len_m;

    // 采样密度（按外侧弧长决定）
    const double sample_ds = 0.05; // 每 5 cm 一点
    int N = std::max(
        subdivision,
        (int)std::ceil(std::abs(totalAngle) * (R_center + 0.5 * width) / sample_ds)
    );
    N = std::clamp(N, 32, 4096);

    // 在本地圆弧坐标（圆心在(0,0)）上发点；s_offset 为“左为正”的横向偏移（米）
    auto emitPoint = [&](double s_offset, double theta, int type_idx) {
        const double R_line = R_center + s_offset;     // 关键：平行圆 => 只改半径
        if (R_line <= 1e-6) return;                    // 守卫

        // 与 Webots CurvedRoadSegment 保持一致的参数化：
        // wayPoint = (R' * sinθ, R' * cosθ, 0)
        const double x_local = R_line * std::sin(theta);   // 沿 +X 切向
        const double y_local = R_line * std::cos(theta);   // 指向圆心的 +Y 方向起点在 +Y

        // 旋转+平移到世界系（只绕Z）
        pcl::PointXYZINormal pt;
        pt.x = t[0] +  cos_y * x_local - sin_y * y_local;
        pt.y = t[1] +  sin_y * x_local + cos_y * y_local;
        pt.z = t[2];
        SemanticType semantic_type = semantic_label.find(static_cast<uint16_t>(type_idx)) == semantic_label.end()
                                         ? SemanticType::NONE
                                         : semantic_label.at(static_cast<uint16_t>(type_idx));
        switch (semantic_type) {
            case SemanticType::LANE:
                pt.intensity = 1.0f;
                break;
            case SemanticType::ROAD_MARKER:
                pt.intensity = 2.0f;
                break;
            case SemanticType::ROAD_SIGN:
                pt.intensity = 3.0f;
                break;
            case SemanticType::CROSSING:
                pt.intensity = 4.0f;
                break;
            case SemanticType::STOPLINE:
                pt.intensity = 5.0f;
                break;
            case SemanticType::POLE:
                pt.intensity = 6.0f;
                break;
            case SemanticType::SURF:
                pt.intensity = 7.0f;
                break;
            case SemanticType::GROUND:
                pt.intensity = 8.0f;
                break;
            default:
                ULOGW("Unknown semantic type");
                break;
        }
        pt.normal_y = type_idx;
        cloud->push_back(pt);
    };

    // 遍历 lanes_cnt+1 条“车道分隔线”（跳过最外侧两条边缘线）
    for (int b = 1; b < lanes_cnt; ++b) {
        const double s = -width * 0.5 + b * lane_width;        // 左为正
        const bool is_center_boundary = (std::abs(s) < 1e-9);
        const bool is_center_dashed   = (lanes_cnt % 2 == 0) && (b == lanes_cnt / 2);

        if (is_center_boundary) {
            // 中心双实线（与中心线平行，半径 R±d/2）
            const double d = 0.20;    // 两条实线之间的间距（米）
            for (int side = -1; side <= 1; side += 2) {
                const double s_line = s + side * (0.5 * d);    // ±d/2
                for (int i = 0; i <= N; ++i) {
                    const double theta = (static_cast<double>(i) / N) * totalAngle;
                    emitPoint(s_line, theta, 126);
                }
            }
        } else {
            // 中心虚线不生成
            if (is_center_dashed) continue;

            // 其它均为虚线：dash/gap 按弧长（米）判断，相位与半径绑定
            const double R_line = R_center + s;
            if (R_line <= 1e-6) continue;

            for (int i = 0; i <= N; ++i) {
                const double theta   = (static_cast<double>(i) / N) * totalAngle;
                const double arc_len = std::abs(theta) * R_line;           // 起点到当前的弧长
                const double phase   = std::fmod(arc_len, period_m);
                if (phase < dash_len_m)
                    emitPoint(s, theta, 125);
            }
        }
    }
}

void DDSWbRoadLine::findRoadSegments(const webots::Node *node)
{
    if (!node) return;
    std::string type = node->getTypeName();
    if (type == "StraightRoadSegment" || type.find("StraightRoadSegment") != std::string::npos || type == "Road") {
        cached_road_segment_nodes_.push_back(node);
    }
    if (type == "CurvedRoadSegment" || type.find("CurvedRoadSegment") != std::string::npos) {
        cached_curved_segment_nodes_.push_back(node);
    }
    // children
    const Field *childrenField = node->getField("children");
    if (childrenField) {
        int childCount = childrenField->getCount();
        for (int i = 0; i < childCount; ++i) {
            const Node *child = childrenField->getMFNode(i);
            if (child) findRoadSegments(child);
        }
    }
    // proto children
    if (node->isProto()) {
        const Field *protoChildren = node->getProtoField("children");
        if (protoChildren) {
            int protoCount = protoChildren->getCount();
            if (protoCount > 0) {
                for (int i = 0; i < protoCount; ++i) {
                    const Node *child = protoChildren->getMFNode(i);
                    if (child) findRoadSegments(child);
                }
            } else {
                const Node *child = protoChildren->getSFNode();
                if (child) findRoadSegments(child);
            }
        }
    }
}

void DDSWbRoadLine::onPublish()
{
    if (!supervisor_) return;

    // double t = supervisor_->getTime();
    // if (t == last_time_) return;
    // last_time_ = t;

    // const Node *root = supervisor_->getRoot();
    // if (root) findRoadSegments(root);
    // // 示例：遍历直道段，提取车道线
    // pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZINormal>());
    // ULOGW("Start extracting lane lines");
    // for (const auto *seg : cached_road_segment_nodes_) {
    //     extractLaneLinesInSegment(seg, cloud);
    // }
    // // TODO: 可扩展到curved_segment_nodes_
    // for (const auto *seg : cached_curved_segment_nodes_) {
    //     extractLaneCurvedInSegment(seg, cloud);  // 和extractLaneLinesInSegment类似提取弯道车道线
    // }
    // ULOGW("Extracted %zu points from road segments", cloud->size());
    // // 保存cloud点云为pcd
    // pcl::io::savePCDFileBinary("/home/<USER>/workspace/uautopilot_simulation/road_lines_long.pcd", *cloud);
    // ULOGW("Finished saving PCD file");
    // 发布cloud
    // if (!cloud->empty()) {
    //     sensor_msgs::msg::PointCloud2 pc_msg;
    //     pcl::toROSMsg(*cloud, pc_msg);
    //     pc_msg.header.frame_id = link_name_;
    //     unav::Time timestamp;
    //     timestamp.fromSec(robot_->getTime());
    //     pc_msg.header.stamp = timestamp;
    //     pc_writer_->Write(pc_msg);
    // }
    // 读取本地路径xyzi格式点云并发布话题
    // pcl::PointCloud<pcl::PointXYZINormal>::Ptr local_cloud(new pcl::PointCloud<pcl::PointXYZINormal>());
    // std::string local_pcd_path = "/home/<USER>/workspace/uautopilot_simulation/road_lines.pcd";
    // if (pcl::io::loadPCDFile<pcl::PointXYZINormal>(local_pcd_path, *local_cloud) == -1) {
    //     ULOGW("Failed to load local point cloud from %s", local_pcd_path.c_str());
    // } else if (!local_cloud->empty()) {
    //     sensor_msgs::msg::PointCloud2 pc_msg;
    //     pcl::toROSMsg(*local_cloud, pc_msg);

    //     pc_msg.header.frame_id = link_name_;
    //     unav::Time timestamp;
    //     timestamp.fromSec(robot_->getTime());
    //     pc_msg.header.stamp = timestamp;
    //     pc_writer_->Write(pc_msg);
    //     ULOGI("Published local point cloud with %lu points.", local_cloud->size());
    // } else {
    //     ULOGW("Local point cloud is empty, nothing to publish.");
    // }

    // if (cloud->empty()) return;
}

bool DDSWbRoadLine::isStepReady()
{
    if (!inited_) return false;
    double t = supervisor_ ? supervisor_->getTime() : 0.0;
    return t != last_time_;
}