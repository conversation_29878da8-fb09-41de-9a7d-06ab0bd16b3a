#include "object_process.hpp"
#include <pcl/features/moment_of_inertia_estimation.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <ulog/ulog.h>
#include <utf/tf2/transform_datatypes.h>
#include <opencv2/core/types.hpp>
#include <opencv2/opencv.hpp>
#include <pcl/impl/point_types.hpp>
#include <regex>

using namespace webots;

ObjectProcess::ObjectProcess(webots::Supervisor *supervisor, webots::Node *main_robot_node) :
    super_robot_(supervisor), main_robot_node_(main_robot_node)
{
}

ObjectProcess::~ObjectProcess() {}

void ObjectProcess::findBoxes(const Node *node, std::vector<const Node *> &boxes)
{
    if (node == NULL) return;

    if (node->getTypeName() == "Box") {
        boxes.push_back(node);
    }

    Field *childrenField = node->getField("children");
    if (childrenField == NULL) return;

    int childCount = childrenField->getCount();
    for (int i = 0; i < childCount; ++i) {
        Node *childNode = childrenField->getMFNode(i);
        if (childNode == NULL) continue;
        findBoxes(childNode, boxes);
    }
}

void ObjectProcess::findBoundingObject(const Node *node, std::vector<const Node *> &output)
{
    if (node == NULL) return;

    Field *bounding_object_field = node->getField("boundingObject");
    if (bounding_object_field != NULL) {
        const Node *bounding_object_node = bounding_object_field->getSFNode();
        if (bounding_object_node) {
            output.push_back(bounding_object_node);
        }
    }
    if (node->isProto()) {
        Field *bounding_object_proto_field = node->getProtoField("boundingObject");
        if (bounding_object_proto_field != NULL) {
            const Node *bounding_object_proto_node = bounding_object_proto_field->getSFNode();
            if (bounding_object_proto_node) {
                output.push_back(bounding_object_proto_node);
            }
        }
    }

    if (node->getDef().find("VEHICLE") == std::string::npos) return;

    Field *childrenField = node->getField("children");
    if (childrenField == NULL) return;

    int childCount = childrenField->getCount();
    for (int i = 0; i < childCount; ++i) {
        Node *childNode = childrenField->getMFNode(i);
        if (childNode == NULL) continue;
        findBoundingObject(childNode, output);
    }
}

BoundingBox3d ObjectProcess::extractBoundingBox(std::vector<WbBoundingObject> &objects)
{
    if (objects.empty()) return BoundingBox3d();

    std::vector<Eigen::Vector3f> points;
    pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>());
    std::vector<cv::Point2f> cv_points;

    double max_z = std::numeric_limits<double>::lowest();
    double min_z = std::numeric_limits<double>::max();
    for (auto &object : objects) {
        std::vector<Eigen::Vector3f> localCorners = {{object.size.x() / 2, object.size.y() / 2, object.size.z() / 2},
                                                     {-object.size.x() / 2, object.size.y() / 2, object.size.z() / 2},
                                                     {object.size.x() / 2, -object.size.y() / 2, object.size.z() / 2},
                                                     {-object.size.x() / 2, -object.size.y() / 2, object.size.z() / 2},
                                                     {object.size.x() / 2, object.size.y() / 2, -object.size.z() / 2},
                                                     {-object.size.x() / 2, object.size.y() / 2, -object.size.z() / 2},
                                                     {object.size.x() / 2, -object.size.y() / 2, -object.size.z() / 2},
                                                     {-object.size.x() / 2, -object.size.y() / 2, -object.size.z() / 2}};

        for (const auto &localCorner : localCorners) {
            Eigen::Vector3f worldCornerVec = object.transform * localCorner;
            points.push_back(worldCornerVec);
            cloud->push_back(pcl::PointXYZ(worldCornerVec.x(), worldCornerVec.y(), 0.0));
            cv_points.push_back(cv::Point2f(worldCornerVec.x(), worldCornerVec.y()));
            if (worldCornerVec.z() > max_z) max_z = worldCornerVec.z();
            if (worldCornerVec.z() < min_z) min_z = worldCornerVec.z();
        }
    }

    // 计算点集的最小外包旋转矩形
    cv::RotatedRect rect = cv::minAreaRect(cv_points);
    cv::Mat vertices;
    cv::boxPoints(rect, vertices);

    BoundingBox3d bounding_box3d;
    bounding_box3d.size.x() = rect.size.height;
    bounding_box3d.size.y() = rect.size.width;
    bounding_box3d.size.z() = max_z - min_z;
    bounding_box3d.translation.x() = rect.center.x;
    bounding_box3d.translation.y() = rect.center.y;
    bounding_box3d.translation.z() = (max_z + min_z) / 2.0;
    bounding_box3d.yaw = 0.0;

    // std::cout << "size:" << bounding_box3d.size.transpose() << std::endl;
    return bounding_box3d;
}

void ObjectProcess::updateUnknownNode()
{
    // 获取webots对象列表
    auto name_list = extractDefNameList();
    for (int i = 0; i < name_list.size(); ++i) {
        std::string def_name = name_list[i].first;
        std::string target_def_prefix = name_list[i].second;

        if (node_list_.find(def_name) != node_list_.end()) continue;

        std::string id_str;
        int id;
        try {
            id_str = def_name.substr(target_def_prefix.length());
            id = std::stoi(id_str);
        } catch (std::exception &e) {
            std::cout << "DDSWbGt::updateNode: getDef failed" << std::endl;
            continue;
        }

        struct NodeData node_data;
        node_data.node_ptr = super_robot_->getFromDef(def_name);
        node_data.def_name = def_name;
        node_data.webots_id = node_data.node_ptr->getId();
        std::cout << "DDSWbGt::updateNode: find object name:" << def_name << " id:" << node_data.node_ptr->getId()
                  << std::endl;

        int prefix_id_base = PrefixIdMap.at(target_def_prefix);
        node_data.def_name_id = id;
        node_data.output_id = prefix_id_base + node_data.def_name_id;
        node_data.prefix = target_def_prefix;

        node_data.webots_object_msg.prefix = target_def_prefix;
        node_data.webots_object_msg.id = id;
        const Field *bounding_object_field = node_data.node_ptr->getField("boundingObject");
        if (bounding_object_field->getSFNode()) {
            Field *bounding_object_size = bounding_object_field->getSFNode()->getField("size");
            node_data.webots_object_msg.size.x = bounding_object_size->getSFVec3f()[0];
            node_data.webots_object_msg.size.y = bounding_object_size->getSFVec3f()[1];
            node_data.webots_object_msg.size.z = bounding_object_size->getSFVec3f()[2];
        }

        const Field *translation_field = node_data.node_ptr->getField("translation");
        if (translation_field->getSFVec3f()) {
            node_data.webots_object_msg.pose.position.x = translation_field->getSFVec3f()[0];
            node_data.webots_object_msg.pose.position.y = translation_field->getSFVec3f()[1];
            node_data.webots_object_msg.pose.position.z = translation_field->getSFVec3f()[2];
        }

        Field *controller_args = node_data.node_ptr->getField("controllerArgs");
        if (controller_args) {
            int args_count = controller_args->getCount();
            for (int i = 0; i < args_count; ++i) {
                std::string arg = controller_args->getMFString(i);
                if (arg.find("--type=") == 0) {
                    std::string type = arg.substr(7);  // Extract type
                    if (type == "cycle")
                        node_data.webots_object_msg.type = webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_CIRCLE;
                    else if (type == "line")
                        node_data.webots_object_msg.type = webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_LINES;
                    else if (type == "polygon")
                        node_data.webots_object_msg.type = webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_POLYGON;
                } else if (arg.find("--velocity=") == 0) {
                    node_data.webots_object_msg.velocity = std::stod(arg.substr(11));  // Extract velocity
                } else if (arg.find("--radius=") == 0) {
                    node_data.webots_object_msg.radius = std::stod(arg.substr(9));  // Extract radius
                } else if (arg.find("--points=") == 0) {
                    std::string points_str = arg.substr(9);  // Extract points
                    std::istringstream points_stream(points_str);
                    std::string point_pair;
                    while (std::getline(points_stream, point_pair, ';')) {
                        std::istringstream pair_stream(point_pair);
                        std::string x_str, y_str;
                        if (std::getline(pair_stream, x_str, ',') && std::getline(pair_stream, y_str, ',')) {
                            geometry_msgs::msg::Point32 point;
                            point.x = std::stod(x_str);
                            point.y = std::stod(y_str);
                            node_data.webots_object_msg.points.push_back(point);
                        }
                    }
                } else if (arg.find("--init_pos=") == 0) {
                    std::string init_pos_str = arg.substr(11);  // Extract init_pos
                    std::istringstream init_pos_stream(init_pos_str);
                    std::string x_str, y_str;
                    if (std::getline(init_pos_stream, x_str, ',') && std::getline(init_pos_stream, y_str, ',')) {
                        node_data.webots_object_msg.pose.position.x = std::stod(x_str);
                        node_data.webots_object_msg.pose.position.y = std::stod(y_str);
                    }
                }
            }
        } else {
            node_data.webots_object_msg.type = webots_objects_msgs::msg::WbObject_Constants::STATIC;
            node_data.webots_object_msg.prefix = "BOX";

            node_data.webots_object_msg.velocity = 0.0;
            node_data.webots_object_msg.radius = 0.0;
        }

        {
            std::cout << "DDSWbGt::updateNode: add node name:" << def_name << " id:" << node_data.node_ptr->getId()
                      << std::endl;
            std::unique_lock<std::shared_mutex> lock(node_list_mutex_);
            node_list_.insert({def_name, node_data});
        }
    }

    std::unique_lock<std::shared_mutex> lock(node_list_mutex_);
    for (auto it = node_list_.begin(); it != node_list_.end();) {
        if (super_robot_->getFromId(it->second.webots_id) == nullptr) {
            std::cout << "DDSWbGt::updateNode: remove node name:" << it->first << std::endl;
            it = node_list_.erase(it);
        } else {
            ++it;
        }
    }
}
void ObjectProcess::updateNode(std::string prefix, int id, const webots_objects_msgs::msg::WbObject &webots_object_msg)
{
    if (PrefixIdMap.find(prefix) == PrefixIdMap.end()) return;
    std::string def_name = prefix + std::to_string(id);
    if (node_list_.find(def_name) == node_list_.end()) {
        struct NodeData node_data;
        node_data.node_ptr = super_robot_->getFromDef(def_name);
        if (!node_data.node_ptr) return;

        node_data.def_name = def_name;
        node_data.webots_id = node_data.node_ptr->getId();

        int prefix_id_base = PrefixIdMap.at(prefix);
        node_data.def_name_id = id;
        node_data.output_id = prefix_id_base + node_data.def_name_id;
        node_data.prefix = prefix;
        node_data.webots_object_msg = webots_object_msg;
        updateObjectPose(&node_data);

        std::unique_lock<std::shared_mutex> lock(node_list_mutex_);
        node_list_.insert({def_name, node_data});

        std::cout << "DDSWbGt::updateNode manual: object name:" << def_name << " id:" << node_data.node_ptr->getId()
                  << std::endl;
    }
}
void ObjectProcess::addBoxObstacleToWb(std::string name, double size_x, double size_y, double size_z, double pos_x,
                                       double pos_y, double pos_theta, double velocity)
{
    // 创建Box物体的PROTO字符串
    std::string boxDef =
        "DEF %s Solid {"
        "  translation %f %f 0"
        "  rotation 0 0 1 %f"
        "  children ["
        "    Shape {"
        "      appearance Appearance {"
        "        material Material {"
        "          diffuseColor 1 0 0"
        "        }"
        "      }"
        "      geometry Box {"
        "        size %f %f %f"
        "      }"
        "    }"
        "  ]"
        "  name \"%s\""
        "  boundingObject Box {"
        "    size %f %f %f"
        "  }"
        "}";

    char box_string[1000];
    sprintf(box_string, boxDef.c_str(), name.c_str(), pos_x, pos_y, pos_theta, size_x, size_y, size_z, name.c_str(),
            size_x, size_y, size_z);

    // 将Box物体添加到场景中
    super_robot_->getRoot()->getField("children")->importMFNodeFromString(-1, box_string);
    // Node *obstacle = super_robot_->getFromDef(name);
    // double velocity_array[6] = {0.0, 0.0, 0.0, 0.0, 0.0, 0.0};
    // velocity_array[0] = velocity * cos(pos_theta);
    // velocity_array[1] = velocity * sin(pos_theta);
    // obstacle->setVelocity(velocity_array);
    printf("addObstacle name:%s size:%f %f %f pos:%f %f theta:%f velocity:%f\n ", name.c_str(), size_x, size_y, size_z,
           pos_x, pos_y, pos_theta, velocity);
}

void ObjectProcess::addDynamicBoxObstacle(std::string name, double size_x, double size_y, double size_z, double pos_x,
                                          double pos_y, std::string type, double velocity, double radius)
{
    if (type != "circle") {
        printf("type %s not support\n", type.c_str());
        return;
    }
    // 创建动态Box物体的PROTO字符串
    std::string dynamic_boxDef =
        "DEF %s Robot {\n"
        "  translation %f %f 0\n"
        "  children [\n"
        "    DBOXUltrasonicSensor {\n"
        "      translation %f 0 0.5\n"
        "    }\n"
        "    Shape {\n"
        "      appearance Appearance {\n"
        "        material Material {\n"
        "          diffuseColor 0 0 1\n"
        "        }\n"
        "      }\n"
        "      geometry Box {\n"
        "        size %f %f %f\n"
        "      }\n"
        "    }\n"
        "  ]\n"
        "  name \"%s\"\n"  // 添加了name属性
        "  boundingObject Box {\n"
        "    size %f %f %f\n"
        "  }\n"
        "  controller \"path_motion\"\n"
        "  controllerArgs [\n"
        "    \"--type=%s\"\n"
        "    \"--radius=%f\"\n"
        "    \"--velocity=%f\"\n"
        "    \"--init_pos=%f,%f,0\"\n"
        "  ]\n"
        "  supervisor TRUE\n"
        "}";

    char box_string[2000];
    sprintf(box_string, dynamic_boxDef.c_str(), name.c_str(), pos_x, pos_y, size_x / 2.0, size_x, size_y, size_z,
            name.c_str(),  // 为name属性添加值
            size_x, size_y, size_z, type.c_str(), radius, velocity, pos_x, pos_y);

    // 将Box物体添加到场景中
    super_robot_->getRoot()->getField("children")->importMFNodeFromString(-1, box_string);
    printf("addDynamicBoxObstacle name:%s size:%f %f %f pos:%f %f type:%s radius:%f velocity:%f\n ", name.c_str(),
           size_x, size_y, size_z, pos_x, pos_y, type.c_str(), radius, velocity);
}

void ObjectProcess::addDynamicBoxObstacle(std::string name, double size_x, double size_y, double size_z, double pos_x,
                                          double pos_y, std::string type, double velocity,
                                          const std::vector<geometry_msgs::msg::Point32> &points)
{
    if (type != "polygon" && type != "line") {
        printf("type %s not support\n", type.c_str());
        return;
    }

    // 将所有路径点转换为字符串
    std::string points_str;
    for (size_t i = 0; i < points.size(); ++i) {
        points_str += std::to_string(points[i].x) + "," + std::to_string(points[i].y);
        if (i < points.size() - 1) {
            points_str += ";";
        }
    }

    // 创建动态Box物体的PROTO字符串
    std::string dynamic_boxDef =
        "DEF %s Robot {\n"
        "  translation %f %f 0\n"
        "  children [\n"
        "    DBOXUltrasonicSensor {\n"
        "      translation %f 0 0.5\n"
        "    }\n"
        "    Shape {\n"
        "      appearance Appearance {\n"
        "        material Material {\n"
        "          diffuseColor 0 0 1\n"
        "        }\n"
        "      }\n"
        "      geometry Box {\n"
        "        size %f %f %f\n"
        "      }\n"
        "    }\n"
        "  ]\n"
        "  name \"%s\"\n"
        "  boundingObject Box {\n"
        "    size %f %f %f\n"
        "  }\n"
        "  controller \"path_motion\"\n"
        "  controllerArgs [\n"
        "    \"--type=%s\"\n"
        "    \"--velocity=%f\"\n"
        "    \"--points=%s\"\n"
        "  ]\n"
        "  supervisor TRUE\n"
        "}";

    char box_string[2000];
    sprintf(box_string, dynamic_boxDef.c_str(), name.c_str(), pos_x, pos_y, size_x / 2.0, size_x, size_y, size_z,
            name.c_str(), size_x, size_y, size_z, type.c_str(), velocity, points_str.c_str());

    // 将Box物体添加到场景中
    super_robot_->getRoot()->getField("children")->importMFNodeFromString(-1, box_string);
    printf("addDynamicBoxObstacle name:%s size:%f %f %f pos:%f %f type:%s points:%s velocity:%f\n", name.c_str(),
           size_x, size_y, size_z, pos_x, pos_y, type.c_str(), points_str.c_str(), velocity);
}

void ObjectProcess::updateBoxObstacleToWb(std::string obs_name, double size_x, double size_y, double size_z,
                                          double pos_x, double pos_y, double pos_theta, double velocity)
{
    Node *obstacle = super_robot_->getFromDef(obs_name);
    if (obstacle == nullptr) {
        printf("obstacle %s not found\n", obs_name.c_str());
        return;
    }

    double velocity_array[6] = {0.0, 0.0, 0.0, 0.0, 0.0, 0.0};
    velocity_array[0] = velocity * cos(pos_theta);
    velocity_array[1] = velocity * sin(pos_theta);
    obstacle->setVelocity(velocity_array);

    double pos_values[3] = {pos_x, pos_y, 0.0};
    Field *translation = obstacle->getField("translation");
    translation->setSFVec3f(pos_values);

    Field *rotation = obstacle->getField("rotation");
    double rotation_values[4] = {0.0, 0.0, 1.0, pos_theta};
    rotation->setSFRotation(rotation_values);

    Field *bounding_object_size = obstacle->getField("boundingObject")->getSFNode()->getField("size");
    double size_values[3] = {size_x, size_y, size_z};
    bounding_object_size->setSFVec3f(size_values);
    const Field *children_field = obstacle->getField("children");
    for (int i = 0; i < children_field->getCount(); i++) {
        auto node = children_field->getMFNode(i);
        if (node->getTypeName() == "Shape") {
            Field *geometry_size = node->getField("geometry")->getSFNode()->getField("size");
            geometry_size->setSFVec3f(size_values);
        }
    }
    printf("updateObstacle name:%s size:%f %f %f pos:%f %f theta:%f velocity:%f\n ", obs_name.c_str(), size_x, size_y,
           size_z, pos_x, pos_y, pos_theta, velocity);
}
void ObjectProcess::updateDynamicBoxObstacle(std::string name, double size_x, double size_y, double size_z, double pos_x,
                                             double pos_y, std::string type, double velocity, double radius)
{
    Node *obstacle = super_robot_->getFromDef(name);
    if (obstacle == nullptr) {
        printf("obstacle %s not found\n", name.c_str());
        return;
    }

    double pos_values[3] = {pos_x, pos_y, 0.0};
    Field *translation = obstacle->getField("translation");
    translation->setSFVec3f(pos_values);

    Field *bounding_object_size = obstacle->getField("boundingObject")->getSFNode()->getField("size");
    double size_values[3] = {size_x, size_y, size_z};
    bounding_object_size->setSFVec3f(size_values);
    const Field *children_field = obstacle->getField("children");
    for (int i = 0; i < children_field->getCount(); i++) {
        auto node = children_field->getMFNode(i);
        if (node->getTypeName() == "Shape") {
            Field *geometry_size = node->getField("geometry")->getSFNode()->getField("size");
            geometry_size->setSFVec3f(size_values);
        }
    }

    // controllerArgs
    Field *controller_args = obstacle->getField("controllerArgs");
    std::string type_str = "--type=" + type;
    controller_args->setMFString(0, type_str.c_str());
    std::string velocity_str = "--velocity=" + std::to_string(velocity);
    controller_args->setMFString(1, velocity_str.c_str());
    std::string radius_str = "--radius=" + std::to_string(radius);
    controller_args->setMFString(2, radius_str.c_str());

    obstacle->restartController();
}

void ObjectProcess::updateDynamicBoxObstacle(std::string name, double size_x, double size_y, double size_z,
                                             double pos_x, double pos_y, std::string type, double velocity,
                                             const std::vector<geometry_msgs::msg::Point32> &points)
{
    Node *obstacle = super_robot_->getFromDef(name);
    if (obstacle == nullptr) {
        printf("obstacle %s not found\n", name.c_str());
        return;
    }

    double pos_values[3] = {pos_x, pos_y, 0.0};
    Field *translation = obstacle->getField("translation");
    translation->setSFVec3f(pos_values);

    Field *bounding_object_size = obstacle->getField("boundingObject")->getSFNode()->getField("size");
    double size_values[3] = {size_x, size_y, size_z};
    bounding_object_size->setSFVec3f(size_values);
    const Field *children_field = obstacle->getField("children");
    for (int i = 0; i < children_field->getCount(); i++) {
        auto node = children_field->getMFNode(i);
        if (node->getTypeName() == "Shape") {
            Field *geometry_size = node->getField("geometry")->getSFNode()->getField("size");
            geometry_size->setSFVec3f(size_values);
        }
    }

    // controllerArgs
    Field *controller_args = obstacle->getField("controllerArgs");
    std::string type_str = "--type=" + type;
    controller_args->setMFString(0, type_str.c_str());
    std::string velocity_str = "--velocity=" + std::to_string(velocity);
    controller_args->setMFString(1, velocity_str.c_str());
    std::string points_str = "--points=";
    for (size_t i = 0; i < points.size(); ++i) {
        points_str += std::to_string(points[i].x) + "," + std::to_string(points[i].y);
        if (i < points.size() - 1) {
            points_str += ";";
        }
    }
    controller_args->setMFString(2, points_str.c_str());

    obstacle->restartController();
}

int ObjectProcess::findMaxId(std::string prefix)
{
    // 寻找最大的id
    int id_max = 0;
    std::unique_lock<std::shared_mutex> lock(node_list_mutex_);
    std::for_each(node_list_.begin(), node_list_.end(), [&](const auto &item) {
        int id = item.second.def_name_id;
        if (item.second.prefix == prefix && id > id_max) {
            id_max = id;
        }
    });
    lock.unlock();
    return id_max;
}

void ObjectProcess::setObstacle(const webots_objects_msgs::msg::WbObject &webots_object_msg, bool in_base_link)
{
    if (webots_object_msg.action != webots_objects_msgs::msg::WbObject_Constants::ADD) {
        return;
    }
    std::string obstable_prefix;
    std::string obstable_dynamic_type;
    if (webots_object_msg.type == webots_objects_msgs::msg::WbObject_Constants::STATIC)
        obstable_prefix = "BOX";
    else if (webots_object_msg.type == webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_POLYGON) {
        obstable_prefix = "DBOX";
        obstable_dynamic_type = "polygon";
    } else if (webots_object_msg.type == webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_CIRCLE) {
        obstable_prefix = "DBOX";
        obstable_dynamic_type = "circle";
    } else if (webots_object_msg.type == webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_LINES) {
        obstable_prefix = "DBOX";
        obstable_dynamic_type = "line";
    } else {
        printf("object type %d not support\n", webots_object_msg.type);
        return;
    }

    auto msg_cpy = webots_object_msg;
    msg_cpy.prefix = obstable_prefix;

    if (in_base_link) {
        Eigen::Vector3f base_pos(msg_cpy.pose.position.x, msg_cpy.pose.position.y, 0);
        const double *pose = main_robot_node_->getPose();
        const double *rotation = main_robot_node_->getOrientation();
        Eigen::Matrix4f matrix;
        matrix << pose[0], pose[1], pose[2], pose[3], pose[4], pose[5], pose[6], pose[7], pose[8], pose[9], pose[10],
            pose[11], pose[12], pose[13], pose[14], pose[15];
        Eigen::Vector3f webots_world_pos = matrix.block<3, 3>(0, 0) * base_pos + matrix.block<3, 1>(0, 3);
        Eigen::Quaternionf quaternion(matrix.block<3, 3>(0, 0));
        tf2::Quaternion tf_quaternion(quaternion.x(), quaternion.y(), quaternion.z(), quaternion.w());
        auto base_theta = tf2::getYaw(tf_quaternion);
        float webots_world_theta = base_theta + tf2::getYaw(msg_cpy.pose.orientation);

        msg_cpy.pose.position.x = webots_world_pos.x();
        msg_cpy.pose.position.y = webots_world_pos.y();
        msg_cpy.pose.orientation = tf2::toMsg(tf2::Quaternion(tf2::Vector3(0, 0, 1), webots_world_theta));

        if (msg_cpy.points.size() > 0) {
            for (auto &point : msg_cpy.points) {
                Eigen::Vector3f base_point(point.x, point.y, 0);
                Eigen::Vector3f webots_world_point = matrix.block<3, 3>(0, 0) * base_point + matrix.block<3, 1>(0, 3);
                std::cout << "base_point: " << base_point.transpose()
                          << " webots_world_point: " << webots_world_point.transpose() << std::endl;
                point.x = webots_world_point.x();
                point.y = webots_world_point.y();
            }
        }
    }

    if (msg_cpy.id == -1) {
        int box_id = findMaxId(obstable_prefix) + 1;
        msg_cpy.id = box_id;
    }

    std::unique_lock<std::shared_mutex> lock(node_list_mutex_);
    std::string name = obstable_prefix + std::to_string(msg_cpy.id);
    bool not_exist = node_list_.find(name) == node_list_.end();
    lock.unlock();
    if (not_exist) {
        if (obstable_prefix == "BOX")
            addBoxObstacleToWb(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                               msg_cpy.pose.position.y, tf2::getYaw(msg_cpy.pose.orientation), msg_cpy.velocity);
        else if (obstable_prefix == "DBOX") {
            if (obstable_dynamic_type == "polygon") {
                addDynamicBoxObstacle(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                                      msg_cpy.pose.position.y, obstable_dynamic_type, msg_cpy.velocity, msg_cpy.points);
            } else if (obstable_dynamic_type == "circle") {
                addDynamicBoxObstacle(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                                      msg_cpy.pose.position.y, obstable_dynamic_type, msg_cpy.velocity, msg_cpy.radius);
            } else if (obstable_dynamic_type == "line") {
                addDynamicBoxObstacle(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                                      msg_cpy.pose.position.y, obstable_dynamic_type, msg_cpy.velocity, msg_cpy.points);
            }
        }
        updateNode(obstable_prefix, msg_cpy.id, msg_cpy);
    } else {
        if (obstable_prefix == "BOX")
            updateBoxObstacleToWb(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                                  msg_cpy.pose.position.y, tf2::getYaw(msg_cpy.pose.orientation), msg_cpy.velocity);
        else if (obstable_prefix == "DBOX") {
            if (obstable_dynamic_type == "polygon") {
                updateDynamicBoxObstacle(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                                         msg_cpy.pose.position.y, obstable_dynamic_type, msg_cpy.velocity, msg_cpy.points);
            } else if (obstable_dynamic_type == "circle") {
                updateDynamicBoxObstacle(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                                         msg_cpy.pose.position.y, obstable_dynamic_type, msg_cpy.velocity, msg_cpy.radius);
            } else if (obstable_dynamic_type == "line") {
                updateDynamicBoxObstacle(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                                         msg_cpy.pose.position.y, obstable_dynamic_type, msg_cpy.velocity, msg_cpy.points);
            }
        }
    }
}

void ObjectProcess::getObstacle(std::vector<std::string> prefix_list, webots_objects_msgs::msg::WbObjectList &webots_objects)
{
    std::shared_lock<std::shared_mutex> lock(node_list_mutex_);
    for (auto &item : node_list_) {
        if (std::find(prefix_list.begin(), prefix_list.end(), item.second.prefix) == prefix_list.end()) continue;
        webots_objects.objects.push_back(item.second.webots_object_msg);
    }
}
void ObjectProcess::getObstacle(int type, webots_objects_msgs::msg::WbObjectList &webots_objects)
{
    std::shared_lock<std::shared_mutex> lock(node_list_mutex_);
    for (auto &item : node_list_) {
        if (type != item.second.webots_object_msg.type) continue;
        webots_objects.objects.push_back(item.second.webots_object_msg);
        printNode(item.second);
    }
    lock.unlock();
}
void ObjectProcess::deleteAllObjects()
{
    std::unique_lock<std::shared_mutex> lock(node_list_mutex_);
    for (auto &item : node_list_) {
        auto node_ptr = super_robot_->getFromId(item.second.webots_id);
        if (node_ptr) {
            node_ptr->remove();
        }
    }
    node_list_.clear();
}

bool ObjectProcess::updateAllObjectsPose()
{
    std::unique_lock<std::shared_mutex> lock(node_list_mutex_);
    for (auto it = node_list_.begin(); it != node_list_.end(); it++) {
        updateObjectPose(&it->second);
    }
    return false;
}

bool ObjectProcess::updateObjectPose(NodeData *node_data)
{
    auto node_ptr = super_robot_->getFromId(node_data->webots_id);
    if (!node_ptr) return false;

    std::vector<const Node *> bounding_object_node_vec;  // bounding_object 对象的指针
    std::vector<const Node *> box_node_vec;              // 需要检测的box的指针和对应的bounding_object

    findBoundingObject(node_ptr, bounding_object_node_vec);
    for (auto &bounding_object_node : bounding_object_node_vec) {
        if (!bounding_object_node) continue;
        std::vector<const Node *> targets;
        findBoxes(bounding_object_node, targets);
        for (auto &target : targets) {
            box_node_vec.push_back(target);
        }
    }

    // printf("name:%s bounding_object_vec size:%ld box_vec size:%ld\n", it->first.c_str(),
    //        bounding_object_node_vec.size(), box_node_vec.size());

    std::vector<WbBoundingObject> bounding_object_vec;  // 所有box 都转到 目标node坐标系下
    for (auto &obj : box_node_vec) {
        Eigen::Isometry3f transform = Eigen::Isometry3f::Identity();
        auto ptr = obj->getParentNode();
        while (ptr != node_ptr) {
            const Field *translation_field = ptr->getField("translation");
            if (translation_field) {
                const Field *rotation_field = ptr->getField("rotation");
                double ax = rotation_field->getSFRotation()[0];
                double ay = rotation_field->getSFRotation()[1];
                double az = rotation_field->getSFRotation()[2];
                double angle = rotation_field->getSFRotation()[3];
                double qx = ax * sin(angle * 0.5);
                double qy = ay * sin(angle * 0.5);
                double qz = az * sin(angle * 0.5);
                double qw = cos(angle * 0.5);

                transform.rotate(Eigen::Quaternionf(qw, qx, qy, qz));
                transform.pretranslate(Eigen::Vector3f(translation_field->getSFVec3f()[0],
                                                       translation_field->getSFVec3f()[1],
                                                       translation_field->getSFVec3f()[2]));
            }
            ptr = ptr->getParentNode();
        }

        WbBoundingObject bo;
        bo.type = "box";
        const Field *size_field = obj->getField("size");
        bo.size.x() = size_field->getSFVec3f()[0];
        bo.size.y() = size_field->getSFVec3f()[1];
        bo.size.z() = size_field->getSFVec3f()[2];
        bo.transform = transform;
        bounding_object_vec.push_back(bo);
    }
    auto boundingbox = extractBoundingBox(bounding_object_vec);  // 提取最大的包围框

    // 世界坐标系
    const double *pose_world = node_ptr->getPose();
    Eigen::Matrix4f matrix_world;
    matrix_world << pose_world[0], pose_world[1], pose_world[2], pose_world[3], pose_world[4], pose_world[5],
        pose_world[6], pose_world[7], pose_world[8], pose_world[9], pose_world[10], pose_world[11], pose_world[12],
        pose_world[13], pose_world[14], pose_world[15];
    Eigen::Quaternionf quaternion_world(matrix_world.block<3, 3>(0, 0));
    Eigen::Vector3f position_world(matrix_world.block<3, 1>(0, 3));

    node_data->webots_object_msg.pose.position.x = position_world[0];
    node_data->webots_object_msg.pose.position.y = position_world[1];
    node_data->webots_object_msg.pose.position.z = position_world[2];
    node_data->webots_object_msg.pose.orientation.x = quaternion_world.x();
    node_data->webots_object_msg.pose.orientation.y = quaternion_world.y();
    node_data->webots_object_msg.pose.orientation.z = quaternion_world.z();
    node_data->webots_object_msg.pose.orientation.w = quaternion_world.w();

    // 机器人坐标系
    const double *pose = node_ptr->getPose(main_robot_node_);
    Eigen::Matrix4f matrix;
    matrix << pose[0], pose[1], pose[2], pose[3], pose[4], pose[5], pose[6], pose[7], pose[8], pose[9], pose[10],
        pose[11], pose[12], pose[13], pose[14], pose[15];
    Eigen::Quaternionf quaternion(matrix.block<3, 3>(0, 0));
    Eigen::Vector3f position(matrix.block<3, 1>(0, 3));

    // std::cout << "bound size:" << boundingbox.size.transpose() << std::endl;
    // std::cout << "bound pos:" << boundingbox.translation.transpose() << std::endl;

    custom_msgs::msg::PerceptionObject object_msg;

    object_msg.pose.orientation.x = quaternion.x();
    object_msg.pose.orientation.y = quaternion.y();
    object_msg.pose.orientation.z = quaternion.z();
    object_msg.pose.orientation.w = quaternion.w();

    double object_yaw = tf2::getYaw(object_msg.pose.orientation);
    double center_x = position[0] + boundingbox.translation.x() * cos(object_yaw);
    double center_y = position[1] + boundingbox.translation.x() * sin(object_yaw);
    object_msg.pose.position.x = center_x;
    object_msg.pose.position.y = center_y;
    object_msg.pose.position.z = position[2] + boundingbox.translation.z();

    // 获取世界坐标系下的速度
    const double *speed_world = node_ptr->getVelocity();

    // 将世界坐标系下的速度转换到机器人坐标系
    Eigen::Vector3f linear_velocity_world(speed_world[0], speed_world[1], speed_world[2]);
    Eigen::Vector3f angular_velocity_world(speed_world[3], speed_world[4], speed_world[5]);

    // 获取机器人在世界坐标系下的姿态
    const double *robot_pose_world = main_robot_node_->getPose();
    Eigen::Matrix4f robot_matrix_world;
    robot_matrix_world << robot_pose_world[0], robot_pose_world[1], robot_pose_world[2], robot_pose_world[3],
        robot_pose_world[4], robot_pose_world[5], robot_pose_world[6], robot_pose_world[7], robot_pose_world[8],
        robot_pose_world[9], robot_pose_world[10], robot_pose_world[11], robot_pose_world[12], robot_pose_world[13],
        robot_pose_world[14], robot_pose_world[15];

    // 计算机器人坐标系到世界坐标系的旋转矩阵
    Eigen::Matrix3f robot_rotation_world = robot_matrix_world.block<3, 3>(0, 0);

    // 将速度从世界坐标系转换到机器人坐标系
    Eigen::Vector3f linear_velocity_robot = robot_rotation_world.transpose() * linear_velocity_world;
    Eigen::Vector3f angular_velocity_robot = robot_rotation_world.transpose() * angular_velocity_world;

    // 设置转换后的速度到消息中
    object_msg.twist.linear.x = linear_velocity_robot.x();
    object_msg.twist.linear.y = linear_velocity_robot.y();
    object_msg.twist.linear.z = linear_velocity_robot.z();
    object_msg.twist.angular.x = angular_velocity_robot.x();
    object_msg.twist.angular.y = angular_velocity_robot.y();
    object_msg.twist.angular.z = angular_velocity_robot.z();

    // 计算机器人坐标系下的线速度和角速度
    double linear_speed = std::sqrt(object_msg.twist.linear.x * object_msg.twist.linear.x +
                                    object_msg.twist.linear.y * object_msg.twist.linear.y);
    double angular_speed = object_msg.twist.angular.z;
    double dt = 0.5;
    size_t judge = 20.0 / linear_speed / dt;
    judge = std::min(judge, size_t(20.0 / dt));

    double cur_yaw = object_yaw;
    unav::Time cur_time = unav::Time::now();
    tf2::Quaternion cur_tf;
    cur_tf.setRPY(0, 0, cur_yaw);
    object_msg.path.emplace_back();
    object_msg.path.back().path.emplace_back();
    object_msg.path.back().path.back().header.stamp = cur_time;
    object_msg.path.back().path.back().pose.position.x = object_msg.pose.position.x;
    object_msg.path.back().path.back().pose.position.y = object_msg.pose.position.y;
    object_msg.path.back().path.back().pose.orientation.w = cur_tf.getW();
    object_msg.path.back().path.back().pose.orientation.x = cur_tf.getX();
    object_msg.path.back().path.back().pose.orientation.y = cur_tf.getY();
    object_msg.path.back().path.back().pose.orientation.z = cur_tf.getZ();
    for (int i = 1; i <= judge; ++i) {
        cur_yaw += angular_speed * dt;
        cur_time = cur_time + unav::Duration(dt);
        cur_tf.setRPY(0, 0, cur_yaw);
        auto last_point = object_msg.path.back().path.back().pose.position;
        object_msg.path.back().path.emplace_back();
        object_msg.path.back().path.back().header.stamp = cur_time;
        object_msg.path.back().path.back().pose.position.x = last_point.x + linear_speed * dt * cos(cur_yaw);
        object_msg.path.back().path.back().pose.position.y = last_point.y + linear_speed * dt * sin(cur_yaw);
        object_msg.path.back().path.back().pose.orientation.w = cur_tf.getW();
        object_msg.path.back().path.back().pose.orientation.x = cur_tf.getX();
        object_msg.path.back().path.back().pose.orientation.y = cur_tf.getY();
        object_msg.path.back().path.back().pose.orientation.z = cur_tf.getZ();
    }
    object_msg.id = node_data->output_id;
    object_msg.size.x = boundingbox.size.x();
    object_msg.size.y = boundingbox.size.y();
    object_msg.size.z = boundingbox.size.z();
    object_msg.oc.classification = custom_msgs::msg::ObjectClassification_Constants::CAR;
    node_data->perception_object_msg = object_msg;

    return true;
}

void ObjectProcess::setRobotPose(double pos_x, double pos_y, double pos_theta)
{
    Field *translation = main_robot_node_->getField("translation");
    double pos_values[3] = {pos_x, pos_y, 0.5};
    translation->setSFVec3f(pos_values);

    Field *rotation = main_robot_node_->getField("rotation");
    double rotation_values[4] = {0.0, 0.0, 1.0, pos_theta};
    rotation->setSFRotation(rotation_values);
}

geometry_msgs::msg::Pose ObjectProcess::getRobotPose()
{
    const double *car_pose = main_robot_node_->getPose();
    Eigen::Matrix4f matrix;
    matrix << car_pose[0], car_pose[1], car_pose[2], car_pose[3], car_pose[4], car_pose[5], car_pose[6], car_pose[7],
        car_pose[8], car_pose[9], car_pose[10], car_pose[11], car_pose[12], car_pose[13], car_pose[14], car_pose[15];
    Eigen::Quaternionf quaternion(matrix.block<3, 3>(0, 0));
    Eigen::Vector3f position(matrix.block<3, 1>(0, 3));

    geometry_msgs::msg::Pose pose;
    pose.position.x = position[0];
    pose.position.y = position[1];
    pose.position.z = position[2];
    pose.orientation.x = quaternion.x();
    pose.orientation.y = quaternion.y();
    pose.orientation.z = quaternion.z();
    pose.orientation.w = quaternion.w();
    return pose;
}

void ObjectProcess::getPerceptionObjects(custom_msgs::msg::PerceptionObjects &perception_objects, double pos_x_min,
                                         double pos_x_max, double pos_y_min, double pos_y_max)
{
    std::shared_lock<std::shared_mutex> lock(node_list_mutex_);
    for (auto &item : node_list_) {
        if (item.second.perception_object_msg.pose.position.x < pos_x_min ||
            item.second.perception_object_msg.pose.position.x > pos_x_max ||
            item.second.perception_object_msg.pose.position.y < pos_y_min ||
            item.second.perception_object_msg.pose.position.y > pos_y_max) {
            continue;
        }
        perception_objects.objects.push_back(item.second.perception_object_msg);
    }
}

void ObjectProcess::printNodeList()
{
    std::shared_lock<std::shared_mutex> lock(node_list_mutex_);
    for (auto &item : node_list_) {
        printf("name:%s webots_id:%d output_id:%d size:%f %f %f pos:%f %f %f type:%d\n", item.first.c_str(),
               item.second.webots_id, item.second.output_id, item.second.webots_object_msg.size.x,
               item.second.webots_object_msg.size.y, item.second.webots_object_msg.size.z,
               item.second.webots_object_msg.pose.position.x, item.second.webots_object_msg.pose.position.y,
               item.second.webots_object_msg.pose.position.z, item.second.webots_object_msg.type);
    }
}

void ObjectProcess::printNode(const NodeData &item)
{
    printf("name:%s webots_id:%d output_id:%d size:%f %f %f pos:%f %f %f type:%d id:%d prefix:%s point_size:%ld\n",
           item.def_name.c_str(), item.webots_id, item.output_id, item.webots_object_msg.size.x,
           item.webots_object_msg.size.y, item.webots_object_msg.size.z, item.webots_object_msg.pose.position.x,
           item.webots_object_msg.pose.position.y, item.webots_object_msg.pose.position.z, item.webots_object_msg.type,
           item.webots_object_msg.id, item.webots_object_msg.prefix.c_str(), item.webots_object_msg.points.size());
}

std::vector<std::pair<std::string, std::string>> ObjectProcess::extractDefNameList()
{
    std::vector<std::pair<std::string, std::string>> defList;
    const webots::Node *root = super_robot_->getRoot();
    std::string content = root->exportString();
    // 正则表达式匹配 DEF 后面的标识符和 Solid/Robot
    std::regex defRegex(R"(DEF\s+(\w+)\s+(Solid|Robot))");
    std::smatch match;

    // 搜索所有匹配项
    for (std::sregex_iterator i = std::sregex_iterator(content.begin(), content.end(), defRegex);
         i != std::sregex_iterator(); ++i) {
        match = *i;
        std::string def_name = match[1].str();  // 提取 DEF 后的标识符

        for (auto &prefix_id : PrefixIdMap) {
            auto target_def_prefix = prefix_id.first;
            if (def_name.find(target_def_prefix) == 0) {
                defList.push_back({def_name, target_def_prefix});
                break;
            }
        }
    }

    // getMFNode API 会严重内存泄漏
    // webots::Node *root = super_robot_->getRoot();
    // const webots::Field *childrenField = root->getField("children");
    // int node_count = childrenField->getCount();
    // for (int i = 0; i < node_count; ++i) {
    //     const webots::Node *node = childrenField->getMFNode(i);
    //     try {
    //         if (node->getDef().empty()) continue;
    //     } catch (std::exception &e) {
    //         continue;
    //     }
    //     std::string def_name = node->getDef();
    //     for (auto &prefix_id : PrefixIdMap) {
    //         auto target_def_prefix = prefix_id.first;
    //         if (def_name.find(target_def_prefix) == 0) defList.push_back(def_name);
    //         break;
    //     }
    // }

    return defList;
}