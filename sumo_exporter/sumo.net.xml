<?xml version="1.0" encoding="UTF-8"?>

<!-- generated on Wed Sep 10 14:48:58 2025 by Eclipse SUMO netedit Version 1.4.0
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://sumo.dlr.de/xsd/netconvertConfiguration.xsd">

    <input>
        <sumo-net-file value="sumo.net.xml"/>
    </input>

    <output>
        <output-file value="sumo.net.xml"/>
    </output>

    <processing>
        <geometry.min-radius.fix.railways value="false"/>
        <geometry.max-grade.fix value="false"/>
        <offset.disable-normalization value="true"/>
        <lefthand value="false"/>
    </processing>

    <junctions>
        <no-turnarounds value="true"/>
        <junctions.corner-detail value="5"/>
        <junctions.limit-turn-speed value="5.5"/>
        <rectangular-lane-cut value="false"/>
    </junctions>

    <pedestrian>
        <walkingareas value="false"/>
    </pedestrian>

    <report>
        <aggregate-warnings value="5"/>
    </report>

</configuration>
-->

<net version="1.3" junctionCornerDetail="5" limitTurnSpeed="5.50" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://sumo.dlr.de/xsd/net_file.xsd">

    <location netOffset="-20.00,133.50" convBoundary="0.00,0.00,113.86,124.78" origBoundary="20.00,-133.50,133.86,-8.72" projParameter="!"/>

    <edge id=":junction_roadc11(1)_roadc12_0" function="internal">
        <lane id=":junction_roadc11(1)_roadc12_0_0" index="0" speed="13.89" length="0.21" width="3.50" shape="41.75,73.50 41.74,73.71"/>
    </edge>
    <edge id=":junction_roadc11(1)_roadc12_1" function="internal">
        <lane id=":junction_roadc11(1)_roadc12_1_0" index="0" speed="13.89" length="0.10" width="3.50" shape="38.25,73.52 38.25,73.50"/>
    </edge>
    <edge id=":junction_roadc11(1)_roadc12(1)_0" function="internal">
        <lane id=":junction_roadc11(1)_roadc12(1)_0_0" index="0" speed="13.89" length="0.10" width="3.50" shape="38.25,63.50 38.25,63.49"/>
    </edge>
    <edge id=":junction_roadc11(1)_roadc12(1)_1" function="internal">
        <lane id=":junction_roadc11(1)_roadc12(1)_1_0" index="0" speed="13.89" length="0.10" width="3.50" shape="41.75,63.42 41.75,63.50"/>
    </edge>
    <edge id=":junction_roadc11(2)_roadc12(1)_0" function="internal">
        <lane id=":junction_roadc11(2)_roadc12(1)_0_0" index="0" speed="13.89" length="0.10" width="3.50" shape="30.03,55.25 30.00,55.25"/>
    </edge>
    <edge id=":junction_roadc11(2)_roadc12(1)_1" function="internal">
        <lane id=":junction_roadc11(2)_roadc12(1)_1_0" index="0" speed="13.89" length="0.21" width="3.50" shape="30.00,51.75 30.21,51.76"/>
    </edge>
    <edge id=":junction_roadc11(2)_roadc12(2)_0" function="internal">
        <lane id=":junction_roadc11(2)_roadc12(2)_0_0" index="0" speed="13.89" length="0.10" width="3.50" shape="10.00,55.25 9.99,55.25"/>
    </edge>
    <edge id=":junction_roadc11(2)_roadc12(2)_1" function="internal">
        <lane id=":junction_roadc11(2)_roadc12(2)_1_0" index="0" speed="13.89" length="0.10" width="3.50" shape="9.91,51.75 10.00,51.75"/>
    </edge>
    <edge id=":junction_roadc11(3)_roadc12(2)_0" function="internal">
        <lane id=":junction_roadc11(3)_roadc12(2)_0_0" index="0" speed="13.89" length="0.21" width="3.50" shape="-1.75,63.50 -1.74,63.29"/>
    </edge>
    <edge id=":junction_roadc11(3)_roadc12(2)_1" function="internal">
        <lane id=":junction_roadc11(3)_roadc12(2)_1_0" index="0" speed="13.89" length="0.10" width="3.50" shape="1.75,63.48 1.75,63.50"/>
    </edge>
    <edge id=":junction_roadc11(3)_roadc12(3)_0" function="internal">
        <lane id=":junction_roadc11(3)_roadc12(3)_0_0" index="0" speed="13.89" length="0.10" width="3.50" shape="-1.75,73.60 -1.75,73.50"/>
    </edge>
    <edge id=":junction_roadc11(3)_roadc12(3)_1" function="internal">
        <lane id=":junction_roadc11(3)_roadc12(3)_1_0" index="0" speed="13.89" length="0.10" width="3.50" shape="1.75,73.50 1.75,73.42"/>
    </edge>
    <edge id=":junction_roadc11(4)_roadc12(4)_0" function="internal">
        <lane id=":junction_roadc11(4)_roadc12(4)_0_0" index="0" speed="13.89" length="0.18" width="3.50" shape="100.09,85.24 99.83,85.25"/>
        <lane id=":junction_roadc11(4)_roadc12(4)_0_1" index="1" speed="13.89" length="0.18" width="3.50" shape="99.92,81.75 99.83,81.75"/>
    </edge>
    <edge id=":junction_roadc11(4)_roadc12(7)_0" function="internal">
        <lane id=":junction_roadc11(4)_roadc12(7)_0_0" index="0" speed="13.89" length="0.26" width="3.50" shape="80.14,85.25 79.82,85.24"/>
        <lane id=":junction_roadc11(4)_roadc12(7)_0_1" index="1" speed="13.89" length="0.26" width="3.50" shape="80.14,81.75 79.95,81.74"/>
    </edge>
    <edge id=":junction_roadc11(5)_roadc12(4)_0" function="internal">
        <lane id=":junction_roadc11(5)_roadc12(4)_0_0" index="0" speed="13.89" length="0.18" width="3.50" shape="111.75,73.33 111.74,73.59"/>
        <lane id=":junction_roadc11(5)_roadc12(4)_0_1" index="1" speed="13.89" length="0.18" width="3.50" shape="108.25,73.33 108.25,73.42"/>
    </edge>
    <edge id=":junction_roadc11(5)_roadc12(5)_0" function="internal">
        <lane id=":junction_roadc11(5)_roadc12(5)_0_0" index="0" speed="13.89" length="0.26" width="3.50" shape="111.74,63.32 111.75,63.64"/>
        <lane id=":junction_roadc11(5)_roadc12(5)_0_1" index="1" speed="13.89" length="0.26" width="3.50" shape="108.24,63.45 108.25,63.64"/>
    </edge>
    <edge id=":junction_roadc11(6)_roadc12(5)_0" function="internal">
        <lane id=":junction_roadc11(6)_roadc12(5)_0_0" index="0" speed="13.89" length="0.18" width="3.50" shape="99.83,51.75 100.09,51.76"/>
        <lane id=":junction_roadc11(6)_roadc12(5)_0_1" index="1" speed="13.89" length="0.18" width="3.50" shape="99.83,55.25 99.92,55.25"/>
    </edge>
    <edge id=":junction_roadc11(6)_roadc12(6)_0" function="internal">
        <lane id=":junction_roadc11(6)_roadc12(6)_0_0" index="0" speed="13.89" length="0.18" width="3.50" shape="79.91,51.76 80.17,51.75"/>
        <lane id=":junction_roadc11(6)_roadc12(6)_0_1" index="1" speed="13.89" length="0.18" width="3.50" shape="80.08,55.25 80.17,55.25"/>
    </edge>
    <edge id=":junction_roadc11(7)_roadc12(6)_0" function="internal">
        <lane id=":junction_roadc11(7)_roadc12(6)_0_0" index="0" speed="13.89" length="0.18" width="3.50" shape="68.25,63.67 68.26,63.41"/>
        <lane id=":junction_roadc11(7)_roadc12(6)_0_1" index="1" speed="13.89" length="0.18" width="3.50" shape="71.75,63.67 71.75,63.58"/>
    </edge>
    <edge id=":junction_roadc11(7)_roadc12(7)_0" function="internal">
        <lane id=":junction_roadc11(7)_roadc12(7)_0_0" index="0" speed="13.89" length="0.18" width="3.50" shape="68.26,73.59 68.25,73.33"/>
        <lane id=":junction_roadc11(7)_roadc12(7)_0_1" index="1" speed="13.89" length="0.18" width="3.50" shape="71.75,73.42 71.75,73.33"/>
    </edge>
    <edge id=":junction_roadc11_roadc12_0" function="internal">
        <lane id=":junction_roadc11_roadc12_0_0" index="0" speed="13.89" length="0.10" width="3.50" shape="30.09,85.25 30.00,85.25"/>
    </edge>
    <edge id=":junction_roadc11_roadc12_1" function="internal">
        <lane id=":junction_roadc11_roadc12_1_0" index="0" speed="13.89" length="0.10" width="3.50" shape="30.00,81.75 30.01,81.75"/>
    </edge>
    <edge id=":junction_roadc11_roadc12(3)_0" function="internal">
        <lane id=":junction_roadc11_roadc12(3)_0_0" index="0" speed="13.89" length="0.20" width="3.50" shape="10.00,85.25 9.80,85.24"/>
    </edge>
    <edge id=":junction_roadc11_roadc12(3)_1" function="internal">
        <lane id=":junction_roadc11_roadc12(3)_1_0" index="0" speed="13.89" length="0.10" width="3.50" shape="9.98,81.75 10.00,81.75"/>
    </edge>
    <edge id=":junction_roadc22_roadc21_0" function="internal">
        <lane id=":junction_roadc22_roadc21_0_0" index="0" speed="13.89" length="0.29" width="3.50" shape="71.75,23.36 71.75,23.56"/>
        <lane id=":junction_roadc22_roadc21_0_1" index="1" speed="13.89" length="0.29" width="3.50" shape="68.25,23.36 68.26,23.74"/>
    </edge>
    <edge id=":junction_roadc22_roadc23_0" function="internal">
        <lane id=":junction_roadc22_roadc23_0_0" index="0" speed="13.89" length="0.29" width="3.50" shape="71.75,13.44 71.75,13.64"/>
        <lane id=":junction_roadc22_roadc23_0_1" index="1" speed="13.89" length="0.29" width="3.50" shape="68.26,13.26 68.25,13.64"/>
    </edge>
    <edge id=":junction_roadc25_roadc23_0" function="internal">
        <lane id=":junction_roadc25_roadc23_0_0" index="0" speed="13.89" length="0.29" width="3.50" shape="80.14,5.25 79.94,5.25"/>
        <lane id=":junction_roadc25_roadc23_0_1" index="1" speed="13.89" length="0.29" width="3.50" shape="80.14,1.75 79.76,1.76"/>
    </edge>
    <edge id=":junction_roadc25_roadc24_0" function="internal">
        <lane id=":junction_roadc25_roadc24_0_0" index="0" speed="13.89" length="0.29" width="3.50" shape="100.06,5.25 99.86,5.25"/>
        <lane id=":junction_roadc25_roadc24_0_1" index="1" speed="13.89" length="0.29" width="3.50" shape="100.24,1.76 99.86,1.75"/>
    </edge>
    <edge id=":junction_roadc26_roadc24_0" function="internal">
        <lane id=":junction_roadc26_roadc24_0_0" index="0" speed="13.89" length="0.29" width="3.50" shape="108.25,13.65 108.25,13.45"/>
        <lane id=":junction_roadc26_roadc24_0_1" index="1" speed="13.89" length="0.29" width="3.50" shape="111.75,13.65 111.74,13.27"/>
    </edge>
    <edge id=":junction_roadc26_roadc28_0" function="internal">
        <lane id=":junction_roadc26_roadc28_0_0" index="0" speed="13.89" length="0.29" width="3.50" shape="108.25,23.56 108.25,23.36"/>
        <lane id=":junction_roadc26_roadc28_0_1" index="1" speed="13.89" length="0.29" width="3.50" shape="111.74,23.74 111.75,23.36"/>
    </edge>
    <edge id=":junction_roadc27_roadc21_0" function="internal">
        <lane id=":junction_roadc27_roadc21_0_0" index="0" speed="13.89" length="0.29" width="3.50" shape="79.95,31.75 80.15,31.75"/>
        <lane id=":junction_roadc27_roadc21_0_1" index="1" speed="13.89" length="0.29" width="3.50" shape="79.77,35.24 80.15,35.25"/>
    </edge>
    <edge id=":junction_roadc27_roadc28_0" function="internal">
        <lane id=":junction_roadc27_roadc28_0_0" index="0" speed="13.89" length="0.29" width="3.50" shape="99.86,31.75 100.06,31.75"/>
        <lane id=":junction_roadc27_roadc28_0_1" index="1" speed="13.89" length="0.29" width="3.50" shape="99.86,35.25 100.24,35.24"/>
    </edge>
    <edge id=":junction_roadf1-b1_end_0" function="internal">
        <lane id=":junction_roadf1-b1_end_0_0" index="0" speed="3.82" length="5.11" width="3.50" shape="17.07,119.71 18.38,120.58 18.82,121.46 18.38,122.33 17.07,123.21"/>
    </edge>
    <edge id=":junction_roadf1-b1_start_0" function="internal">
        <lane id=":junction_roadf1-b1_start_0_0" index="0" speed="3.82" length="5.11" width="3.50" shape="7.07,123.21 5.76,122.33 5.32,121.46 5.76,120.58 7.07,119.71"/>
    </edge>
    <edge id=":junction_roadf1-b2_end_0" function="internal">
        <lane id=":junction_roadf1-b2_end_0_0" index="0" speed="3.11" length="3.40" width="2.33" shape="31.66,119.20 32.53,119.79 32.83,120.37 32.53,120.95 31.66,121.54"/>
    </edge>
    <edge id=":junction_roadf1-b2_start_0" function="internal">
        <lane id=":junction_roadf1-b2_start_0_0" index="0" speed="3.11" length="3.40" width="2.33" shape="21.66,121.54 20.79,120.95 20.49,120.37 20.79,119.79 21.66,119.20"/>
    </edge>
    <edge id=":junction_roadpi/2_end_0" function="internal">
        <lane id=":junction_roadpi/2_end_0_0" index="0" speed="3.82" length="5.11" width="3.50" shape="76.50,101.93 77.43,100.65 78.32,100.25 79.18,100.72 80.00,102.07"/>
    </edge>
    <edge id=":junction_roadpi/2_start_0" function="internal">
        <lane id=":junction_roadpi/2_start_0_0" index="0" speed="3.82" length="5.11" width="3.50" shape="68.34,113.75 66.98,112.94 66.50,112.09 66.89,111.19 68.16,110.25"/>
    </edge>
    <edge id=":junction_roadrad2f1_end_0" function="internal">
        <lane id=":junction_roadrad2f1_end_0_0" index="0" speed="3.82" length="5.11" width="3.50" shape="91.62,99.37 91.96,97.83 92.62,97.11 93.60,97.20 94.88,98.11"/>
    </edge>
    <edge id=":junction_roadrad2f1_start_0" function="internal">
        <lane id=":junction_roadrad2f1_start_0_0" index="0" speed="3.82" length="5.11" width="3.50" shape="84.26,114.65 82.90,113.85 82.41,113.00 82.80,112.10 84.06,111.15"/>
    </edge>

    <edge id="-roadb3" from="junction_roadb3_end" to="junction_roadb3_start" priority="-1">
        <lane id="-roadb3_0" index="0" speed="13.89" length="10.00" width="2.33" shape="60.50,123.64 50.50,123.64"/>
        <lane id="-roadb3_1" index="1" speed="13.89" length="10.00" width="2.33" shape="60.50,121.31 50.50,121.31"/>
        <lane id="-roadb3_2" index="2" speed="13.89" length="10.00" width="2.33" shape="60.50,118.98 50.50,118.98"/>
    </edge>
    <edge id="-roadc11" from="junction_roadc11_roadc12" to="junction_roadc11_roadc12(3)" priority="-1">
        <lane id="-roadc11_0" index="0" speed="13.89" length="20.00" width="3.50" shape="30.00,85.25 10.00,85.25"/>
    </edge>
    <edge id="-roadc11(1)" from="junction_roadc11(1)_roadc12(1)" to="junction_roadc11(1)_roadc12" priority="-1">
        <lane id="-roadc11(1)_0" index="0" speed="13.89" length="10.00" width="3.50" shape="41.75,63.50 41.75,73.50"/>
    </edge>
    <edge id="-roadc11(2)" from="junction_roadc11(2)_roadc12(2)" to="junction_roadc11(2)_roadc12(1)" priority="-1">
        <lane id="-roadc11(2)_0" index="0" speed="13.89" length="20.00" width="3.50" shape="10.00,51.75 30.00,51.75"/>
    </edge>
    <edge id="-roadc11(3)" from="junction_roadc11(3)_roadc12(3)" to="junction_roadc11(3)_roadc12(2)" priority="-1">
        <lane id="-roadc11(3)_0" index="0" speed="13.89" length="10.00" width="3.50" shape="-1.75,73.50 -1.75,63.50"/>
    </edge>
    <edge id="-roadc11(4)" from="junction_roadc11(4)_roadc12(4)" to="junction_roadc11(4)_roadc12(7)" priority="-1">
        <lane id="-roadc11(4)_0" index="0" speed="13.89" length="19.69" width="3.50" shape="99.83,85.25 80.14,85.25"/>
        <lane id="-roadc11(4)_1" index="1" speed="13.89" length="19.69" width="3.50" shape="99.83,81.75 80.14,81.75"/>
    </edge>
    <edge id="-roadc11(5)" from="junction_roadc11(5)_roadc12(5)" to="junction_roadc11(5)_roadc12(4)" priority="-1">
        <lane id="-roadc11(5)_0" index="0" speed="13.89" length="9.69" width="3.50" shape="111.75,63.64 111.75,73.33"/>
        <lane id="-roadc11(5)_1" index="1" speed="13.89" length="9.69" width="3.50" shape="108.25,63.64 108.25,73.33"/>
    </edge>
    <edge id="-roadc11(6)" from="junction_roadc11(6)_roadc12(6)" to="junction_roadc11(6)_roadc12(5)" priority="-1">
        <lane id="-roadc11(6)_0" index="0" speed="13.89" length="19.66" width="3.50" shape="80.17,51.75 99.83,51.75"/>
        <lane id="-roadc11(6)_1" index="1" speed="13.89" length="19.66" width="3.50" shape="80.17,55.25 99.83,55.25"/>
    </edge>
    <edge id="-roadc11(7)" from="junction_roadc11(7)_roadc12(7)" to="junction_roadc11(7)_roadc12(6)" priority="-1">
        <lane id="-roadc11(7)_0" index="0" speed="13.89" length="9.66" width="3.50" shape="68.25,73.33 68.25,63.67"/>
        <lane id="-roadc11(7)_1" index="1" speed="13.89" length="9.66" width="3.50" shape="71.75,73.33 71.75,63.67"/>
    </edge>
    <edge id="-roadc12" from="junction_roadc11(1)_roadc12" to="junction_roadc11_roadc12" priority="-1" shape="40.00,73.50 39.95,74.48 39.81,75.45 39.57,76.40 39.24,77.33 38.82,78.21 38.31,79.06 37.73,79.84 37.07,80.57 36.34,81.23 35.56,81.81 34.71,82.32 33.83,82.74 32.90,83.07 31.95,83.31 30.98,83.45 30.00,83.50">
        <lane id="-roadc12_0" index="0" speed="13.89" length="18.15" width="3.50" shape="41.74,73.71 41.69,74.65 41.53,75.79 41.24,76.91 40.86,78.00 40.36,79.04 39.76,80.03 39.08,80.95 38.31,81.81 37.45,82.58 36.53,83.26 35.54,83.86 34.50,84.36 33.41,84.74 32.29,85.03 31.15,85.19 30.09,85.25"/>
    </edge>
    <edge id="-roadc12(1)" from="junction_roadc11(2)_roadc12(1)" to="junction_roadc11(1)_roadc12(1)" priority="-1" shape="30.01,53.50 30.99,53.55 31.96,53.69 32.91,53.93 33.83,54.26 34.72,54.68 35.56,55.19 36.35,55.77 37.08,56.43 37.74,57.16 38.32,57.95 38.82,58.79 39.24,59.68 39.57,60.60 39.81,61.56 39.95,62.53 40.00,63.51">
        <lane id="-roadc12(1)_0" index="0" speed="13.89" length="18.16" width="3.50" shape="30.21,51.76 31.16,51.81 32.30,51.97 33.42,52.26 34.50,52.64 35.55,53.14 36.53,53.74 37.46,54.41 38.32,55.19 39.10,56.05 39.78,56.98 40.37,57.97 40.86,59.01 41.24,60.09 41.53,61.22 41.69,62.36 41.75,63.42"/>
    </edge>
    <edge id="-roadc12(2)" from="junction_roadc11(3)_roadc12(2)" to="junction_roadc11(2)_roadc12(2)" priority="-1" shape="0.00,63.50 0.05,62.52 0.19,61.55 0.43,60.60 0.76,59.67 1.18,58.79 1.68,57.95 2.27,57.16 2.93,56.43 3.66,55.77 4.44,55.19 5.29,54.68 6.17,54.26 7.10,53.93 8.05,53.69 9.02,53.55 10.00,53.50">
        <lane id="-roadc12(2)_0" index="0" speed="13.89" length="18.15" width="3.50" shape="-1.74,63.29 -1.69,62.35 -1.53,61.21 -1.24,60.09 -0.86,59.00 -0.36,57.96 0.22,56.98 0.92,56.05 1.69,55.19 2.55,54.42 3.47,53.74 4.46,53.14 5.50,52.64 6.59,52.26 7.71,51.97 8.85,51.81 9.91,51.75"/>
    </edge>
    <edge id="-roadc12(3)" from="junction_roadc11_roadc12(3)" to="junction_roadc11(3)_roadc12(3)" priority="-1" shape="10.01,83.50 9.03,83.45 8.06,83.31 7.10,83.07 6.18,82.74 5.29,82.32 4.45,81.82 3.66,81.24 2.93,80.58 2.27,79.85 1.69,79.06 1.18,78.22 0.76,77.33 0.43,76.41 0.19,75.46 0.05,74.49 0.00,73.51">
        <lane id="-roadc12(3)_0" index="0" speed="13.89" length="18.15" width="3.50" shape="9.80,85.24 8.86,85.19 7.72,85.03 6.59,84.74 5.51,84.36 4.47,83.87 3.48,83.28 2.55,82.60 1.69,81.82 0.91,80.96 0.24,80.03 -0.36,79.05 -0.86,78.00 -1.24,76.92 -1.53,75.80 -1.69,74.66 -1.75,73.60"/>
    </edge>
    <edge id="-roadc12(4)" from="junction_roadc11(5)_roadc12(4)" to="junction_roadc11(4)_roadc12(4)" priority="-1" shape="106.50,73.33 106.46,74.14 106.37,74.77 106.22,75.39 106.00,75.99 105.73,76.56 105.40,77.11 105.02,77.62 104.59,78.09 104.12,78.52 103.61,78.90 103.06,79.23 102.49,79.50 101.89,79.72 101.27,79.87 100.64,79.96 99.83,80.00">
        <lane id="-roadc12(4)_0" index="0" speed="13.89" length="15.69" width="3.50" shape="111.74,73.59 111.69,74.61 111.53,75.76 111.24,76.91 110.84,78.02 110.36,79.04 109.76,80.03 109.07,80.96 108.30,81.80 107.46,82.57 106.53,83.26 105.54,83.86 104.52,84.34 103.41,84.74 102.26,85.03 101.11,85.19 100.09,85.24"/>
        <lane id="-roadc12(4)_1" index="1" speed="13.89" length="15.69" width="3.50" shape="108.25,73.42 108.20,74.30 108.09,75.10 107.89,75.90 107.61,76.67 107.27,77.39 106.85,78.08 106.37,78.73 105.83,79.33 105.23,79.87 104.58,80.35 103.89,80.77 103.17,81.11 102.40,81.39 101.60,81.59 100.80,81.70 99.92,81.75"/>
    </edge>
    <edge id="-roadc12(5)" from="junction_roadc11(6)_roadc12(5)" to="junction_roadc11(5)_roadc12(5)" priority="-1" shape="99.83,57.00 100.64,57.04 101.27,57.13 101.89,57.29 102.49,57.50 103.07,57.77 103.61,58.10 104.12,58.48 104.60,58.91 105.02,59.38 105.40,59.90 105.73,60.44 106.00,61.02 106.22,61.62 106.37,62.24 106.47,62.87 106.50,63.68">
        <lane id="-roadc12(5)_0" index="0" speed="13.89" length="15.58" width="3.50" shape="100.09,51.76 101.11,51.81 102.30,51.98 103.41,52.27 104.47,52.64 105.55,53.14 106.55,53.75 107.44,54.42 108.31,55.20 109.10,56.08 109.76,56.98 110.36,57.96 110.85,59.01 111.24,60.10 111.52,61.21 111.70,62.40 111.74,63.32"/>
        <lane id="-roadc12(5)_1" index="1" speed="13.89" length="15.58" width="3.50" shape="99.92,55.25 100.80,55.30 101.61,55.41 102.40,55.62 103.15,55.88 103.90,56.23 104.59,56.65 105.23,57.13 105.84,57.67 106.38,58.28 106.85,58.93 107.27,59.61 107.62,60.35 107.89,61.11 108.09,61.90 108.21,62.71 108.24,63.45"/>
    </edge>
    <edge id="-roadc12(6)" from="junction_roadc11(7)_roadc12(6)" to="junction_roadc11(6)_roadc12(6)" priority="-1" shape="73.50,63.67 73.54,62.86 73.63,62.23 73.78,61.61 74.00,61.01 74.27,60.44 74.60,59.89 74.98,59.38 75.41,58.91 75.88,58.48 76.39,58.10 76.94,57.77 77.51,57.50 78.11,57.28 78.73,57.13 79.36,57.04 80.17,57.00">
        <lane id="-roadc12(6)_0" index="0" speed="13.89" length="15.69" width="3.50" shape="68.26,63.41 68.31,62.39 68.47,61.24 68.76,60.09 69.16,58.98 69.64,57.96 70.24,56.97 70.93,56.04 71.70,55.20 72.54,54.43 73.47,53.74 74.46,53.14 75.48,52.66 76.59,52.26 77.74,51.97 78.89,51.81 79.91,51.76"/>
        <lane id="-roadc12(6)_1" index="1" speed="13.89" length="15.69" width="3.50" shape="71.75,63.58 71.80,62.70 71.91,61.90 72.11,61.10 72.39,60.33 72.73,59.61 73.15,58.92 73.63,58.27 74.17,57.67 74.77,57.13 75.42,56.65 76.11,56.23 76.83,55.89 77.60,55.61 78.40,55.41 79.20,55.30 80.08,55.25"/>
    </edge>
    <edge id="-roadc12(7)" from="junction_roadc11(4)_roadc12(7)" to="junction_roadc11(7)_roadc12(7)" priority="-1" shape="80.18,80.00 79.37,79.97 78.74,79.87 78.12,79.72 77.52,79.50 76.94,79.23 76.40,78.90 75.88,78.52 75.41,78.10 74.98,77.62 74.60,77.11 74.27,76.57 74.00,75.99 73.79,75.39 73.63,74.77 73.54,74.14 73.50,73.33">
        <lane id="-roadc12(7)_0" index="0" speed="13.89" length="15.58" width="3.50" shape="79.82,85.24 78.90,85.20 77.71,85.02 76.60,84.74 75.51,84.35 74.46,83.86 73.48,83.26 72.58,82.60 71.70,81.81 70.92,80.94 70.25,80.05 69.64,79.05 69.14,77.97 68.77,76.91 68.48,75.80 68.31,74.61 68.26,73.59"/>
        <lane id="-roadc12(7)_1" index="1" speed="13.89" length="15.58" width="3.50" shape="79.95,81.74 79.21,81.71 78.40,81.59 77.61,81.39 76.85,81.12 76.11,80.77 75.43,80.35 74.78,79.88 74.17,79.34 73.63,78.73 73.15,78.09 72.73,77.40 72.38,76.65 72.12,75.90 71.91,75.11 71.80,74.30 71.75,73.42"/>
    </edge>
    <edge id="-roadf1-b1" from="junction_roadf1-b1_end" to="junction_roadf1-b1_start" priority="-1">
        <lane id="-roadf1-b1_0" index="0" speed="13.89" length="10.00" width="3.50" shape="17.07,123.21 7.07,123.21"/>
    </edge>
    <edge id="-roadf1-b2" from="junction_roadf1-b2_end" to="junction_roadf1-b2_start" priority="-1">
        <lane id="-roadf1-b2_0" index="0" speed="13.89" length="10.00" width="2.33" shape="31.66,123.87 21.66,123.87"/>
        <lane id="-roadf1-b2_1" index="1" speed="13.89" length="10.00" width="2.33" shape="31.66,121.54 21.66,121.54"/>
    </edge>
    <edge id="-roadpi/2" from="junction_roadpi/2_end" to="junction_roadpi/2_start" priority="-1" shape="78.25,102.00 78.21,102.98 78.06,103.95 77.82,104.91 77.49,105.83 77.07,106.72 76.57,107.56 75.98,108.35 75.32,109.07 74.60,109.73 73.81,110.32 72.97,110.82 72.08,111.24 71.16,111.57 70.20,111.81 69.23,111.95 68.25,112.00">
        <lane id="-roadpi/2_0" index="0" speed="13.89" length="18.29" width="3.50" shape="80.00,102.07 79.95,103.15 79.78,104.30 79.49,105.42 79.11,106.50 78.62,107.54 78.03,108.53 77.33,109.47 76.56,110.31 75.72,111.08 74.78,111.78 73.79,112.37 72.75,112.86 71.67,113.24 70.54,113.53 69.40,113.69 68.34,113.75"/>
    </edge>
    <edge id="-roadrad2f1" from="junction_roadrad2f1_end" to="junction_roadrad2f1_start" priority="-1" shape="93.25,98.74 93.70,99.91 94.00,101.12 94.14,102.36 94.13,103.61 93.97,104.85 93.65,106.06 93.18,107.22 92.57,108.31 91.83,109.31 90.98,110.22 90.01,111.01 88.95,111.68 87.82,112.21 86.63,112.59 85.41,112.83 84.16,112.90">
        <lane id="-roadrad2f1_0" index="0" speed="13.89" length="23.29" width="3.50" shape="94.88,98.11 95.37,99.38 95.73,100.81 95.89,102.27 95.88,103.73 95.69,105.19 95.31,106.61 94.76,107.98 94.04,109.26 93.18,110.43 92.18,111.50 91.03,112.43 89.79,113.22 88.46,113.84 87.07,114.29 85.63,114.57 84.26,114.65"/>
    </edge>
    <edge id="roadc11" from="junction_roadc11_roadc12(3)" to="junction_roadc11_roadc12" priority="-1">
        <lane id="roadc11_0" index="0" speed="13.89" length="20.00" width="3.50" shape="10.00,81.75 30.00,81.75"/>
    </edge>
    <edge id="roadc11(1)" from="junction_roadc11(1)_roadc12" to="junction_roadc11(1)_roadc12(1)" priority="-1">
        <lane id="roadc11(1)_0" index="0" speed="13.89" length="10.00" width="3.50" shape="38.25,73.50 38.25,63.50"/>
    </edge>
    <edge id="roadc11(2)" from="junction_roadc11(2)_roadc12(1)" to="junction_roadc11(2)_roadc12(2)" priority="-1">
        <lane id="roadc11(2)_0" index="0" speed="13.89" length="20.00" width="3.50" shape="30.00,55.25 10.00,55.25"/>
    </edge>
    <edge id="roadc11(3)" from="junction_roadc11(3)_roadc12(2)" to="junction_roadc11(3)_roadc12(3)" priority="-1">
        <lane id="roadc11(3)_0" index="0" speed="13.89" length="10.00" width="3.50" shape="1.75,63.50 1.75,73.50"/>
    </edge>
    <edge id="roadc12" from="junction_roadc11_roadc12" to="junction_roadc11(1)_roadc12" priority="-1" shape="30.00,83.50 30.98,83.45 31.95,83.31 32.90,83.07 33.83,82.74 34.71,82.32 35.56,81.81 36.34,81.23 37.07,80.57 37.73,79.84 38.31,79.06 38.82,78.21 39.24,77.33 39.57,76.40 39.81,75.45 39.95,74.48 40.00,73.50">
        <lane id="roadc12_0" index="0" speed="13.89" length="12.91" width="3.50" shape="30.01,81.75 30.81,81.71 31.61,81.59 32.39,81.40 33.16,81.12 33.88,80.78 34.59,80.36 35.23,79.88 35.83,79.33 36.38,78.73 36.86,78.09 37.28,77.38 37.62,76.66 37.90,75.89 38.09,75.11 38.21,74.31 38.25,73.52"/>
    </edge>
    <edge id="roadc12(1)" from="junction_roadc11(1)_roadc12(1)" to="junction_roadc11(2)_roadc12(1)" priority="-1" shape="40.00,63.51 39.95,62.53 39.81,61.56 39.57,60.60 39.24,59.68 38.82,58.79 38.32,57.95 37.74,57.16 37.08,56.43 36.35,55.77 35.56,55.19 34.72,54.68 33.83,54.26 32.91,53.93 31.96,53.69 30.99,53.55 30.01,53.50">
        <lane id="roadc12(1)_0" index="0" speed="13.89" length="12.92" width="3.50" shape="38.25,63.49 38.21,62.70 38.09,61.90 37.90,61.11 37.62,60.35 37.27,59.61 36.86,58.92 36.38,58.27 35.84,57.67 35.24,57.13 34.59,56.64 33.89,56.22 33.16,55.88 32.40,55.60 31.62,55.41 30.82,55.29 30.03,55.25"/>
    </edge>
    <edge id="roadc12(2)" from="junction_roadc11(2)_roadc12(2)" to="junction_roadc11(3)_roadc12(2)" priority="-1" shape="10.00,53.50 9.02,53.55 8.05,53.69 7.10,53.93 6.17,54.26 5.29,54.68 4.44,55.19 3.66,55.77 2.93,56.43 2.27,57.16 1.68,57.95 1.18,58.79 0.76,59.67 0.43,60.60 0.19,61.55 0.05,62.52 0.00,63.50">
        <lane id="roadc12(2)_0" index="0" speed="13.89" length="12.91" width="3.50" shape="9.99,55.25 9.19,55.29 8.39,55.41 7.61,55.60 6.84,55.88 6.12,56.22 5.41,56.64 4.77,57.12 4.17,57.67 3.62,58.27 3.14,58.92 2.72,59.62 2.38,60.34 2.10,61.11 1.91,61.89 1.79,62.69 1.75,63.48"/>
    </edge>
    <edge id="roadc12(3)" from="junction_roadc11(3)_roadc12(3)" to="junction_roadc11_roadc12(3)" priority="-1" shape="0.00,73.51 0.05,74.49 0.19,75.46 0.43,76.41 0.76,77.33 1.18,78.22 1.69,79.06 2.27,79.85 2.93,80.58 3.66,81.24 4.45,81.82 5.29,82.32 6.18,82.74 7.10,83.07 8.06,83.31 9.03,83.45 10.01,83.50">
        <lane id="roadc12(3)_0" index="0" speed="13.89" length="13.02" width="3.50" shape="1.75,73.42 1.79,74.32 1.91,75.12 2.10,75.90 2.38,76.66 2.72,77.39 3.14,78.09 3.63,78.74 4.17,79.34 4.77,79.88 5.42,80.36 6.11,80.77 6.85,81.12 7.61,81.40 8.40,81.59 9.20,81.71 9.98,81.75"/>
    </edge>
    <edge id="roadc21" from="junction_roadc22_roadc21" to="junction_roadc27_roadc21" priority="-1" shape="66.50,23.68 66.55,24.66 66.59,25.00 66.73,25.98 66.80,26.31 67.04,27.26 67.14,27.59 67.47,28.52 67.60,28.83 68.02,29.72 68.18,30.02 68.69,30.86 68.88,31.15 69.47,31.94 69.68,32.20 70.34,32.93 70.59,33.17 71.31,33.83 71.58,34.05 72.37,34.63 72.65,34.82 73.50,35.33 73.80,35.49 74.69,35.91 75.00,36.04 75.93,36.37 76.26,36.47 77.21,36.71 77.55,36.77 78.52,36.92 78.86,36.95 79.84,37.00">
        <lane id="roadc21_0" index="0" speed="13.89" length="15.41" width="3.50" shape="71.75,23.56 71.79,24.30 71.91,25.15 72.11,25.91 72.39,26.69 72.74,27.42 73.15,28.10 73.65,28.76 74.17,29.34 74.80,29.91 75.44,30.37 76.14,30.79 76.88,31.14 77.63,31.40 78.40,31.60 79.23,31.72 79.95,31.75"/>
        <lane id="roadc21_1" index="1" speed="13.89" length="15.41" width="3.50" shape="68.26,23.74 68.30,24.54 68.32,24.76 68.46,25.70 68.50,25.90 68.73,26.81 68.80,27.02 69.11,27.91 69.19,28.10 69.59,28.95 69.69,29.13 70.18,29.94 70.30,30.13 70.86,30.88 70.99,31.04 71.62,31.73 71.78,31.89 72.47,32.52 72.63,32.65 73.39,33.21 73.57,33.33 74.38,33.82 74.57,33.92 75.42,34.32 75.61,34.40 76.50,34.71 76.71,34.78 77.61,35.01 77.83,35.04 78.76,35.19 78.97,35.20 79.77,35.24"/>
    </edge>
    <edge id="roadc22" from="junction_roadc22_roadc23" to="junction_roadc22_roadc21" priority="-1">
        <lane id="roadc22_0" index="0" speed="13.89" length="9.72" width="3.50" shape="71.75,13.64 71.75,23.36"/>
        <lane id="roadc22_1" index="1" speed="13.89" length="9.72" width="3.50" shape="68.25,13.64 68.25,23.36"/>
    </edge>
    <edge id="roadc23" from="junction_roadc25_roadc23" to="junction_roadc22_roadc23" priority="-1" shape="79.83,0.00 78.85,0.05 78.51,0.09 77.53,0.23 77.20,0.30 76.25,0.54 75.92,0.64 74.99,0.97 74.68,1.10 73.79,1.52 73.49,1.68 72.64,2.18 72.36,2.37 71.57,2.96 71.30,3.18 70.58,3.84 70.33,4.08 69.68,4.81 69.46,5.07 68.87,5.86 68.68,6.15 68.18,6.99 68.02,7.29 67.60,8.18 67.47,8.50 67.13,9.42 67.04,9.75 66.80,10.70 66.73,11.04 66.59,12.01 66.55,12.35 66.50,13.33">
        <lane id="roadc23_0" index="0" speed="13.89" length="15.40" width="3.50" shape="79.94,5.25 79.21,5.29 78.36,5.41 77.60,5.61 76.82,5.89 76.09,6.24 75.38,6.66 74.76,7.13 74.15,7.69 73.62,8.28 73.13,8.93 72.72,9.62 72.38,10.35 72.09,11.13 71.90,11.93 71.79,12.72 71.75,13.44"/>
        <lane id="roadc23_1" index="1" speed="13.89" length="15.40" width="3.50" shape="79.76,1.76 78.97,1.80 78.75,1.82 77.81,1.96 77.61,2.00 76.70,2.23 76.49,2.30 75.60,2.61 75.41,2.69 74.56,3.09 74.36,3.20 73.55,3.67 73.39,3.78 72.63,4.35 72.46,4.49 71.77,5.12 71.61,5.27 70.99,5.97 70.85,6.14 70.29,6.88 70.17,7.06 69.69,7.87 69.59,8.06 69.19,8.90 69.11,9.12 68.78,9.99 68.73,10.19 68.50,11.11 68.46,11.32 68.32,12.25 68.30,12.47 68.26,13.26"/>
    </edge>
    <edge id="roadc24" from="junction_roadc26_roadc24" to="junction_roadc25_roadc24" priority="-1" shape="113.50,13.34 113.45,12.36 113.42,12.02 113.27,11.05 113.21,10.71 112.97,9.76 112.87,9.43 112.54,8.50 112.41,8.19 111.99,7.30 111.83,7.00 111.32,6.15 111.13,5.87 110.55,5.08 110.33,4.81 109.67,4.09 109.43,3.84 108.70,3.18 108.44,2.97 107.65,2.38 107.36,2.19 106.52,1.68 106.22,1.52 105.33,1.10 105.02,0.97 104.09,0.64 103.76,0.54 102.81,0.30 102.48,0.23 101.50,0.09 101.16,0.05 100.18,0.00">
        <lane id="roadc24_0" index="0" speed="13.89" length="15.41" width="3.50" shape="108.25,13.45 108.21,12.68 108.09,11.88 107.90,11.11 107.62,10.33 107.27,9.60 106.86,8.91 106.36,8.24 105.82,7.66 105.23,7.12 104.58,6.64 103.86,6.21 103.14,5.87 102.39,5.61 101.57,5.40 100.79,5.29 100.06,5.25"/>
        <lane id="roadc24_1" index="1" speed="13.89" length="15.41" width="3.50" shape="111.74,13.27 111.70,12.47 111.69,12.26 111.54,11.33 111.51,11.11 111.28,10.21 111.21,10.00 110.90,9.11 110.82,8.92 110.42,8.07 110.32,7.88 109.83,7.07 109.71,6.89 109.15,6.13 109.02,5.97 108.39,5.28 108.23,5.12 107.54,4.49 107.38,4.36 106.63,3.80 106.44,3.68 105.63,3.19 105.45,3.09 104.60,2.69 104.41,2.61 103.52,2.30 103.31,2.23 102.40,2.00 102.20,1.96 101.26,1.82 101.04,1.80 100.24,1.76"/>
    </edge>
    <edge id="roadc25" from="junction_roadc25_roadc24" to="junction_roadc25_roadc23" priority="-1">
        <lane id="roadc25_0" index="0" speed="13.89" length="19.72" width="3.50" shape="99.86,5.25 80.14,5.25"/>
        <lane id="roadc25_1" index="1" speed="13.89" length="19.72" width="3.50" shape="99.86,1.75 80.14,1.75"/>
    </edge>
    <edge id="roadc26" from="junction_roadc26_roadc28" to="junction_roadc26_roadc24" priority="-1">
        <lane id="roadc26_0" index="0" speed="13.89" length="9.71" width="3.50" shape="108.25,23.36 108.25,13.65"/>
        <lane id="roadc26_1" index="1" speed="13.89" length="9.71" width="3.50" shape="111.75,23.36 111.75,13.65"/>
    </edge>
    <edge id="roadc27" from="junction_roadc27_roadc21" to="junction_roadc27_roadc28" priority="-1">
        <lane id="roadc27_0" index="0" speed="13.89" length="19.71" width="3.50" shape="80.15,31.75 99.86,31.75"/>
        <lane id="roadc27_1" index="1" speed="13.89" length="19.71" width="3.50" shape="80.15,35.25 99.86,35.25"/>
    </edge>
    <edge id="roadc28" from="junction_roadc27_roadc28" to="junction_roadc26_roadc28" priority="-1" shape="100.17,37.00 101.15,36.95 101.49,36.91 102.46,36.77 102.80,36.70 103.75,36.46 104.08,36.36 105.01,36.03 105.32,35.90 106.21,35.48 106.51,35.32 107.36,34.82 107.64,34.63 108.43,34.04 108.69,33.82 109.42,33.16 109.66,32.92 110.32,32.19 110.54,31.93 111.13,31.14 111.32,30.86 111.82,30.01 111.98,29.71 112.40,28.82 112.53,28.51 112.86,27.58 112.96,27.25 113.20,26.30 113.27,25.96 113.41,24.99 113.45,24.65 113.50,23.67">
        <lane id="roadc28_0" index="0" speed="13.89" length="15.40" width="3.50" shape="100.06,31.75 100.79,31.71 101.63,31.59 102.40,31.39 103.18,31.11 103.91,30.76 104.62,30.34 105.22,29.88 105.85,29.31 106.40,28.70 106.89,28.05 107.27,27.40 107.63,26.63 107.89,25.88 108.10,25.07 108.21,24.28 108.25,23.56"/>
        <lane id="roadc28_1" index="1" speed="13.89" length="15.40" width="3.50" shape="100.24,35.24 101.03,35.20 101.25,35.18 102.18,35.04 102.39,35.00 103.30,34.77 103.51,34.70 104.40,34.39 104.59,34.31 105.44,33.91 105.64,33.80 106.45,33.33 106.61,33.22 107.36,32.65 107.53,32.51 108.23,31.88 108.38,31.73 109.01,31.03 109.15,30.86 109.72,30.11 109.83,29.95 110.30,29.14 110.41,28.94 110.81,28.09 110.89,27.90 111.20,27.01 111.27,26.80 111.50,25.89 111.54,25.68 111.68,24.75 111.70,24.53 111.74,23.74"/>
    </edge>
    <edge id="roadf1-b1" from="junction_roadf1-b1_start" to="junction_roadf1-b1_end" priority="-1">
        <lane id="roadf1-b1_0" index="0" speed="13.89" length="10.00" width="3.50" shape="7.07,119.71 17.07,119.71"/>
    </edge>
    <edge id="roadf1-b2" from="junction_roadf1-b2_start" to="junction_roadf1-b2_end" priority="-1">
        <lane id="roadf1-b2_0" index="0" speed="13.89" length="10.00" width="2.33" shape="21.66,119.20 31.66,119.20"/>
    </edge>
    <edge id="roadf3" from="junction_roadf3_start" to="junction_roadf3_end" priority="-1">
        <lane id="roadf3_0" index="0" speed="13.89" length="10.00" width="2.33" shape="36.13,118.95 46.13,118.95"/>
        <lane id="roadf3_1" index="1" speed="13.89" length="10.00" width="2.33" shape="36.13,121.28 46.13,121.28"/>
        <lane id="roadf3_2" index="2" speed="13.89" length="10.00" width="2.33" shape="36.13,123.61 46.13,123.61"/>
    </edge>
    <edge id="roadpi/2" from="junction_roadpi/2_start" to="junction_roadpi/2_end" priority="-1" shape="68.25,112.00 69.23,111.95 70.20,111.81 71.16,111.57 72.08,111.24 72.97,110.82 73.81,110.32 74.60,109.73 75.32,109.07 75.98,108.35 76.57,107.56 77.07,106.72 77.49,105.83 77.82,104.91 78.06,103.95 78.21,102.98 78.25,102.00">
        <lane id="roadpi/2_0" index="0" speed="13.89" length="13.12" width="3.50" shape="68.16,110.25 69.06,110.21 69.86,110.09 70.65,109.90 71.41,109.62 72.15,109.27 72.84,108.86 73.48,108.38 74.08,107.83 74.63,107.23 75.11,106.59 75.52,105.90 75.87,105.16 76.15,104.40 76.34,103.60 76.47,102.81 76.50,101.93"/>
    </edge>
    <edge id="roadrad2f1" from="junction_roadrad2f1_start" to="junction_roadrad2f1_end" priority="-1" shape="84.16,112.90 85.41,112.83 86.63,112.59 87.82,112.21 88.95,111.68 90.01,111.01 90.98,110.22 91.83,109.31 92.57,108.31 93.18,107.22 93.65,106.06 93.97,104.85 94.13,103.61 94.14,102.36 94.00,101.12 93.70,99.91 93.25,98.74">
        <lane id="roadrad2f1_0" index="0" speed="13.89" length="16.69" width="3.50" shape="84.06,111.15 85.19,111.09 86.19,110.89 87.18,110.58 88.11,110.14 88.99,109.59 89.78,108.94 90.48,108.19 91.10,107.36 91.60,106.46 91.99,105.51 92.25,104.51 92.38,103.49 92.39,102.45 92.27,101.43 92.03,100.44 91.62,99.37"/>
    </edge>
    <edge id="roadrad2f2" from="junction_roadrad2f2_start" to="junction_roadrad2f2_end" priority="-1" shape="100.59,116.39 101.84,116.32 102.27,116.26 103.50,116.03 103.93,115.92 105.11,115.54 105.52,115.38 106.65,114.85 107.04,114.64 108.09,113.97 108.45,113.72 109.41,112.92 109.73,112.62 110.59,111.72 110.87,111.38 111.61,110.38 111.85,110.01 112.46,108.92 112.64,108.52 113.11,107.36 113.25,106.95 113.57,105.74 113.65,105.31 113.82,104.07 113.85,103.64 113.86,102.39 113.84,101.95 113.69,100.71 113.61,100.28 113.31,99.07 113.18,98.65 112.74,97.48">
        <lane id="roadrad2f2_0" index="0" speed="13.89" length="19.99" width="3.50" shape="100.30,111.15 101.43,111.09 102.45,110.89 103.42,110.57 104.35,110.13 105.22,109.57 105.99,108.94 106.73,108.17 107.34,107.33 107.82,106.46 108.22,105.46 108.47,104.50 108.61,103.45 108.61,102.42 108.49,101.43 108.23,100.41 107.83,99.33"/>
        <lane id="roadrad2f2_1" index="1" speed="13.89" length="19.99" width="3.50" shape="100.49,114.64 101.70,114.58 101.97,114.54 103.15,114.32 103.42,114.25 104.55,113.88 104.80,113.78 105.88,113.28 106.13,113.15 107.13,112.50 107.39,112.33 108.27,111.59 108.48,111.39 109.30,110.54 109.48,110.32 110.19,109.36 110.34,109.13 110.91,108.10 111.02,107.85 111.48,106.73 111.57,106.47 111.87,105.33 111.92,105.05 112.08,103.86 112.10,103.60 112.11,102.40 112.10,102.13 111.96,100.95 111.91,100.68 111.62,99.52 111.53,99.24 111.10,98.10"/>
    </edge>

    <junction id="junction_roadb3_end" type="dead_end" x="60.50" y="117.81" incLanes="" intLanes="" shape="60.50,117.81 60.50,124.80"/>
    <junction id="junction_roadb3_start" type="dead_end" x="50.50" y="117.81" incLanes="-roadb3_0 -roadb3_1 -roadb3_2" intLanes="" shape="50.50,124.80 50.50,117.81"/>
    <junction id="junction_roadc11(1)_roadc12" type="priority" x="40.00" y="73.50" incLanes="-roadc11(1)_0 roadc12_0" intLanes=":junction_roadc11(1)_roadc12_0_0 :junction_roadc11(1)_roadc12_1_0" shape="43.50,73.41 36.50,73.41 43.49,73.80">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc11(1)_roadc12(1)" type="priority" x="40.00" y="63.50" incLanes="roadc11(1)_0 -roadc12(1)_0" intLanes=":junction_roadc11(1)_roadc12(1)_0_0 :junction_roadc11(1)_roadc12(1)_1_0" shape="36.50,63.60 43.50,63.60 43.49,63.25 36.50,63.57">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc11(2)_roadc12(1)" type="priority" x="30.00" y="53.50" incLanes="roadc12(1)_0 -roadc11(2)_0" intLanes=":junction_roadc11(2)_roadc12(1)_0_0 :junction_roadc11(2)_roadc12(1)_1_0" shape="29.94,57.00 30.30,50.01 29.92,50.00 29.92,57.00">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc11(2)_roadc12(2)" type="priority" x="10.00" y="53.50" incLanes="roadc11(2)_0 -roadc12(2)_0" intLanes=":junction_roadc11(2)_roadc12(2)_0_0 :junction_roadc11(2)_roadc12(2)_1_0" shape="10.09,57.00 10.09,50.00 9.74,50.01 10.07,57.00">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc11(3)_roadc12(2)" type="priority" x="0.00" y="63.50" incLanes="-roadc11(3)_0 roadc12(2)_0" intLanes=":junction_roadc11(3)_roadc12(2)_0_0 :junction_roadc11(3)_roadc12(2)_1_0" shape="-3.50,63.59 3.50,63.59 -3.49,63.20">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc11(3)_roadc12(3)" type="priority" x="0.00" y="73.50" incLanes="-roadc12(3)_0 roadc11(3)_0" intLanes=":junction_roadc11(3)_roadc12(3)_0_0 :junction_roadc11(3)_roadc12(3)_1_0" shape="-3.49,73.76 3.50,73.44 -3.50,73.41">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc11(4)_roadc12(4)" type="priority" x="100.00" y="80.00" incLanes="-roadc12(4)_0 -roadc12(4)_1" intLanes=":junction_roadc11(4)_roadc12(4)_0_0 :junction_roadc11(4)_roadc12(4)_0_1" shape="100.19,86.99 99.83,80.00 99.83,87.00">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc11(4)_roadc12(7)" type="priority" x="80.00" y="80.00" incLanes="-roadc11(4)_0 -roadc11(4)_1" intLanes=":junction_roadc11(4)_roadc12(7)_0_0 :junction_roadc11(4)_roadc12(7)_0_1" shape="80.14,87.00 80.14,80.00 80.02,79.99 79.75,86.99">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc11(5)_roadc12(4)" type="priority" x="106.50" y="73.50" incLanes="-roadc11(5)_0 -roadc11(5)_1" intLanes=":junction_roadc11(5)_roadc12(4)_0_0 :junction_roadc11(5)_roadc12(4)_0_1" shape="113.50,73.33 106.50,73.33 113.49,73.69">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc11(5)_roadc12(5)" type="priority" x="106.50" y="63.50" incLanes="-roadc12(5)_0 -roadc12(5)_1" intLanes=":junction_roadc11(5)_roadc12(5)_0_0 :junction_roadc11(5)_roadc12(5)_0_1" shape="106.50,63.64 113.50,63.64 113.49,63.25 106.49,63.52">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc11(6)_roadc12(5)" type="priority" x="100.00" y="57.00" incLanes="-roadc11(6)_0 -roadc11(6)_1" intLanes=":junction_roadc11(6)_roadc12(5)_0_0 :junction_roadc11(6)_roadc12(5)_0_1" shape="99.83,57.00 100.19,50.01 99.83,50.00">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc11(6)_roadc12(6)" type="priority" x="80.00" y="57.00" incLanes="-roadc12(6)_0 -roadc12(6)_1" intLanes=":junction_roadc11(6)_roadc12(6)_0_0 :junction_roadc11(6)_roadc12(6)_0_1" shape="80.17,57.00 80.17,50.00 79.81,50.01">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc11(7)_roadc12(6)" type="priority" x="73.50" y="63.50" incLanes="-roadc11(7)_0 -roadc11(7)_1" intLanes=":junction_roadc11(7)_roadc12(6)_0_0 :junction_roadc11(7)_roadc12(6)_0_1" shape="66.50,63.67 73.50,63.67 66.51,63.31">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc11(7)_roadc12(7)" type="priority" x="73.50" y="73.50" incLanes="-roadc12(7)_0 -roadc12(7)_1" intLanes=":junction_roadc11(7)_roadc12(7)_0_0 :junction_roadc11(7)_roadc12(7)_0_1" shape="66.51,73.69 73.50,73.33 66.50,73.33">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc11_roadc12" type="priority" x="30.00" y="83.50" incLanes="-roadc12_0 roadc11_0" intLanes=":junction_roadc11_roadc12_0_0 :junction_roadc11_roadc12_1_0" shape="30.26,86.99 29.93,80.00 29.91,87.00">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc11_roadc12(3)" type="priority" x="10.00" y="83.50" incLanes="-roadc11_0 roadc12(3)_0" intLanes=":junction_roadc11_roadc12(3)_0_0 :junction_roadc11_roadc12(3)_1_0" shape="10.09,87.00 10.09,80.00 9.71,86.99">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc22_roadc21" type="priority" x="66.50" y="23.50" incLanes="roadc22_0 roadc22_1" intLanes=":junction_roadc22_roadc21_0_0 :junction_roadc22_roadc21_0_1" shape="66.51,23.83 73.50,23.47 73.50,23.36 66.50,23.36">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc22_roadc23" type="priority" x="66.50" y="13.50" incLanes="roadc23_0 roadc23_1" intLanes=":junction_roadc22_roadc23_0_0 :junction_roadc22_roadc23_0_1" shape="66.50,13.64 73.50,13.64 73.50,13.53 66.51,13.18">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc25_roadc23" type="priority" x="80.00" y="0.00" incLanes="roadc25_0 roadc25_1" intLanes=":junction_roadc25_roadc23_0_0 :junction_roadc25_roadc23_0_1" shape="80.14,7.00 80.14,0.00 79.68,0.01 80.03,7.00">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc25_roadc24" type="priority" x="100.00" y="0.00" incLanes="roadc24_0 roadc24_1" intLanes=":junction_roadc25_roadc24_0_0 :junction_roadc25_roadc24_0_1" shape="99.97,7.00 100.33,0.01 99.86,0.00 99.86,7.00">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc26_roadc24" type="priority" x="113.50" y="13.50" incLanes="roadc26_0 roadc26_1" intLanes=":junction_roadc26_roadc24_0_0 :junction_roadc26_roadc24_0_1" shape="106.50,13.65 113.50,13.65 113.49,13.18 106.50,13.54">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc26_roadc28" type="priority" x="113.50" y="23.50" incLanes="roadc28_0 roadc28_1" intLanes=":junction_roadc26_roadc28_0_0 :junction_roadc26_roadc28_0_1" shape="113.50,23.36 106.50,23.36 106.50,23.47 113.49,23.82">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc27_roadc21" type="priority" x="80.00" y="37.00" incLanes="roadc21_0 roadc21_1" intLanes=":junction_roadc27_roadc21_0_0 :junction_roadc27_roadc21_0_1" shape="80.15,37.00 80.15,30.00 80.04,30.00 79.68,36.99">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadc27_roadc28" type="priority" x="100.00" y="37.00" incLanes="roadc27_0 roadc27_1" intLanes=":junction_roadc27_roadc28_0_0 :junction_roadc27_roadc28_0_1" shape="100.32,36.99 99.97,30.00 99.86,30.00 99.86,37.00">
        <request index="0" response="00" foes="00" cont="0"/>
        <request index="1" response="00" foes="00" cont="0"/>
    </junction>
    <junction id="junction_roadf1-b1_end" type="priority" x="17.07" y="121.46" incLanes="roadf1-b1_0" intLanes=":junction_roadf1-b1_end_0_0" shape="17.07,121.46 17.07,117.96 17.07,121.46">
        <request index="0" response="0" foes="0" cont="0"/>
    </junction>
    <junction id="junction_roadf1-b1_start" type="priority" x="7.07" y="121.46" incLanes="-roadf1-b1_0" intLanes=":junction_roadf1-b1_start_0_0" shape="7.07,121.46 7.07,124.96 7.07,121.46">
        <request index="0" response="0" foes="0" cont="0"/>
    </junction>
    <junction id="junction_roadf1-b2_end" type="priority" x="31.66" y="120.37" incLanes="roadf1-b2_0" intLanes=":junction_roadf1-b2_end_0_0" shape="31.66,120.37 31.66,118.04 31.66,120.37">
        <request index="0" response="0" foes="0" cont="0"/>
    </junction>
    <junction id="junction_roadf1-b2_start" type="priority" x="21.66" y="120.37" incLanes="-roadf1-b2_0 -roadf1-b2_1" intLanes=":junction_roadf1-b2_start_0_0" shape="21.66,120.37 21.66,125.03 21.66,120.37">
        <request index="0" response="0" foes="0" cont="0"/>
    </junction>
    <junction id="junction_roadf3_end" type="dead_end" x="46.13" y="124.78" incLanes="roadf3_0 roadf3_1 roadf3_2" intLanes="" shape="46.13,117.79 46.13,124.78"/>
    <junction id="junction_roadf3_start" type="dead_end" x="36.13" y="124.78" incLanes="" intLanes="" shape="36.13,124.78 36.13,117.79"/>
    <junction id="junction_roadpi/2_end" type="priority" x="78.25" y="102.00" incLanes="roadpi/2_0" intLanes=":junction_roadpi/2_end_0_0" shape="78.25,102.00 81.75,102.14 74.75,101.87">
        <request index="0" response="0" foes="0" cont="0"/>
    </junction>
    <junction id="junction_roadpi/2_start" type="priority" x="68.25" y="112.00" incLanes="-roadpi/2_0" intLanes=":junction_roadpi/2_start_0_0" shape="68.25,112.00 68.07,108.50 68.43,115.50">
        <request index="0" response="0" foes="0" cont="0"/>
    </junction>
    <junction id="junction_roadrad2f1_end" type="priority" x="93.25" y="98.74" incLanes="roadrad2f1_0" intLanes=":junction_roadrad2f1_end_0_0" shape="93.25,98.74 89.98,100.00 93.25,98.74">
        <request index="0" response="0" foes="0" cont="0"/>
    </junction>
    <junction id="junction_roadrad2f1_start" type="priority" x="84.16" y="112.90" incLanes="-roadrad2f1_0" intLanes=":junction_roadrad2f1_start_0_0" shape="84.16,112.90 84.36,116.39 84.16,112.90">
        <request index="0" response="0" foes="0" cont="0"/>
    </junction>
    <junction id="junction_roadrad2f2_end" type="dead_end" x="112.74" y="97.48" incLanes="roadrad2f2_0 roadrad2f2_1" intLanes="" shape="106.19,99.95 112.74,97.48"/>
    <junction id="junction_roadrad2f2_start" type="dead_end" x="100.59" y="116.39" incLanes="" intLanes="" shape="100.59,116.39 100.20,109.40"/>

    <connection from="-roadc11" to="-roadc12(3)" fromLane="0" toLane="0" via=":junction_roadc11_roadc12(3)_0_0" dir="s" state="M"/>
    <connection from="-roadc11(1)" to="-roadc12" fromLane="0" toLane="0" via=":junction_roadc11(1)_roadc12_0_0" dir="s" state="M"/>
    <connection from="-roadc11(2)" to="-roadc12(1)" fromLane="0" toLane="0" via=":junction_roadc11(2)_roadc12(1)_1_0" dir="s" state="M"/>
    <connection from="-roadc11(3)" to="-roadc12(2)" fromLane="0" toLane="0" via=":junction_roadc11(3)_roadc12(2)_0_0" dir="s" state="M"/>
    <connection from="-roadc11(4)" to="-roadc12(7)" fromLane="0" toLane="0" via=":junction_roadc11(4)_roadc12(7)_0_0" dir="s" state="M"/>
    <connection from="-roadc11(4)" to="-roadc12(7)" fromLane="1" toLane="1" via=":junction_roadc11(4)_roadc12(7)_0_1" dir="s" state="M"/>
    <connection from="-roadc11(5)" to="-roadc12(4)" fromLane="0" toLane="0" via=":junction_roadc11(5)_roadc12(4)_0_0" dir="s" state="M"/>
    <connection from="-roadc11(5)" to="-roadc12(4)" fromLane="1" toLane="1" via=":junction_roadc11(5)_roadc12(4)_0_1" dir="s" state="M"/>
    <connection from="-roadc11(6)" to="-roadc12(5)" fromLane="0" toLane="0" via=":junction_roadc11(6)_roadc12(5)_0_0" dir="s" state="M"/>
    <connection from="-roadc11(6)" to="-roadc12(5)" fromLane="1" toLane="1" via=":junction_roadc11(6)_roadc12(5)_0_1" dir="s" state="M"/>
    <connection from="-roadc11(7)" to="-roadc12(6)" fromLane="0" toLane="0" via=":junction_roadc11(7)_roadc12(6)_0_0" dir="s" state="M"/>
    <connection from="-roadc11(7)" to="-roadc12(6)" fromLane="1" toLane="1" via=":junction_roadc11(7)_roadc12(6)_0_1" dir="s" state="M"/>
    <connection from="-roadc12" to="-roadc11" fromLane="0" toLane="0" via=":junction_roadc11_roadc12_0_0" dir="s" state="M"/>
    <connection from="-roadc12(1)" to="-roadc11(1)" fromLane="0" toLane="0" via=":junction_roadc11(1)_roadc12(1)_1_0" dir="s" state="M"/>
    <connection from="-roadc12(2)" to="-roadc11(2)" fromLane="0" toLane="0" via=":junction_roadc11(2)_roadc12(2)_1_0" dir="s" state="M"/>
    <connection from="-roadc12(3)" to="-roadc11(3)" fromLane="0" toLane="0" via=":junction_roadc11(3)_roadc12(3)_0_0" dir="s" state="M"/>
    <connection from="-roadc12(4)" to="-roadc11(4)" fromLane="0" toLane="0" via=":junction_roadc11(4)_roadc12(4)_0_0" dir="s" state="M"/>
    <connection from="-roadc12(4)" to="-roadc11(4)" fromLane="1" toLane="1" via=":junction_roadc11(4)_roadc12(4)_0_1" dir="s" state="M"/>
    <connection from="-roadc12(5)" to="-roadc11(5)" fromLane="0" toLane="0" via=":junction_roadc11(5)_roadc12(5)_0_0" dir="s" state="M"/>
    <connection from="-roadc12(5)" to="-roadc11(5)" fromLane="1" toLane="1" via=":junction_roadc11(5)_roadc12(5)_0_1" dir="s" state="M"/>
    <connection from="-roadc12(6)" to="-roadc11(6)" fromLane="0" toLane="0" via=":junction_roadc11(6)_roadc12(6)_0_0" dir="s" state="M"/>
    <connection from="-roadc12(6)" to="-roadc11(6)" fromLane="1" toLane="1" via=":junction_roadc11(6)_roadc12(6)_0_1" dir="s" state="M"/>
    <connection from="-roadc12(7)" to="-roadc11(7)" fromLane="0" toLane="0" via=":junction_roadc11(7)_roadc12(7)_0_0" dir="s" state="M"/>
    <connection from="-roadc12(7)" to="-roadc11(7)" fromLane="1" toLane="1" via=":junction_roadc11(7)_roadc12(7)_0_1" dir="s" state="M"/>
    <connection from="-roadf1-b1" to="roadf1-b1" fromLane="0" toLane="0" via=":junction_roadf1-b1_start_0_0" dir="t" state="M"/>
    <connection from="-roadf1-b2" to="roadf1-b2" fromLane="1" toLane="0" via=":junction_roadf1-b2_start_0_0" dir="t" state="M"/>
    <connection from="-roadpi/2" to="roadpi/2" fromLane="0" toLane="0" via=":junction_roadpi/2_start_0_0" dir="t" state="M"/>
    <connection from="-roadrad2f1" to="roadrad2f1" fromLane="0" toLane="0" via=":junction_roadrad2f1_start_0_0" dir="t" state="M"/>
    <connection from="roadc11" to="roadc12" fromLane="0" toLane="0" via=":junction_roadc11_roadc12_1_0" dir="s" state="M"/>
    <connection from="roadc11(1)" to="roadc12(1)" fromLane="0" toLane="0" via=":junction_roadc11(1)_roadc12(1)_0_0" dir="s" state="M"/>
    <connection from="roadc11(2)" to="roadc12(2)" fromLane="0" toLane="0" via=":junction_roadc11(2)_roadc12(2)_0_0" dir="s" state="M"/>
    <connection from="roadc11(3)" to="roadc12(3)" fromLane="0" toLane="0" via=":junction_roadc11(3)_roadc12(3)_1_0" dir="s" state="M"/>
    <connection from="roadc12" to="roadc11(1)" fromLane="0" toLane="0" via=":junction_roadc11(1)_roadc12_1_0" dir="s" state="M"/>
    <connection from="roadc12(1)" to="roadc11(2)" fromLane="0" toLane="0" via=":junction_roadc11(2)_roadc12(1)_0_0" dir="s" state="M"/>
    <connection from="roadc12(2)" to="roadc11(3)" fromLane="0" toLane="0" via=":junction_roadc11(3)_roadc12(2)_1_0" dir="s" state="M"/>
    <connection from="roadc12(3)" to="roadc11" fromLane="0" toLane="0" via=":junction_roadc11_roadc12(3)_1_0" dir="s" state="M"/>
    <connection from="roadc21" to="roadc27" fromLane="0" toLane="0" via=":junction_roadc27_roadc21_0_0" dir="s" state="M"/>
    <connection from="roadc21" to="roadc27" fromLane="1" toLane="1" via=":junction_roadc27_roadc21_0_1" dir="s" state="M"/>
    <connection from="roadc22" to="roadc21" fromLane="0" toLane="0" via=":junction_roadc22_roadc21_0_0" dir="s" state="M"/>
    <connection from="roadc22" to="roadc21" fromLane="1" toLane="1" via=":junction_roadc22_roadc21_0_1" dir="s" state="M"/>
    <connection from="roadc23" to="roadc22" fromLane="0" toLane="0" via=":junction_roadc22_roadc23_0_0" dir="s" state="M"/>
    <connection from="roadc23" to="roadc22" fromLane="1" toLane="1" via=":junction_roadc22_roadc23_0_1" dir="s" state="M"/>
    <connection from="roadc24" to="roadc25" fromLane="0" toLane="0" via=":junction_roadc25_roadc24_0_0" dir="s" state="M"/>
    <connection from="roadc24" to="roadc25" fromLane="1" toLane="1" via=":junction_roadc25_roadc24_0_1" dir="s" state="M"/>
    <connection from="roadc25" to="roadc23" fromLane="0" toLane="0" via=":junction_roadc25_roadc23_0_0" dir="s" state="M"/>
    <connection from="roadc25" to="roadc23" fromLane="1" toLane="1" via=":junction_roadc25_roadc23_0_1" dir="s" state="M"/>
    <connection from="roadc26" to="roadc24" fromLane="0" toLane="0" via=":junction_roadc26_roadc24_0_0" dir="s" state="M"/>
    <connection from="roadc26" to="roadc24" fromLane="1" toLane="1" via=":junction_roadc26_roadc24_0_1" dir="s" state="M"/>
    <connection from="roadc27" to="roadc28" fromLane="0" toLane="0" via=":junction_roadc27_roadc28_0_0" dir="s" state="M"/>
    <connection from="roadc27" to="roadc28" fromLane="1" toLane="1" via=":junction_roadc27_roadc28_0_1" dir="s" state="M"/>
    <connection from="roadc28" to="roadc26" fromLane="0" toLane="0" via=":junction_roadc26_roadc28_0_0" dir="s" state="M"/>
    <connection from="roadc28" to="roadc26" fromLane="1" toLane="1" via=":junction_roadc26_roadc28_0_1" dir="s" state="M"/>
    <connection from="roadf1-b1" to="-roadf1-b1" fromLane="0" toLane="0" via=":junction_roadf1-b1_end_0_0" dir="t" state="M"/>
    <connection from="roadf1-b2" to="-roadf1-b2" fromLane="0" toLane="1" via=":junction_roadf1-b2_end_0_0" dir="t" state="M"/>
    <connection from="roadpi/2" to="-roadpi/2" fromLane="0" toLane="0" via=":junction_roadpi/2_end_0_0" dir="t" state="M"/>
    <connection from="roadrad2f1" to="-roadrad2f1" fromLane="0" toLane="0" via=":junction_roadrad2f1_end_0_0" dir="t" state="M"/>

    <connection from=":junction_roadc11(1)_roadc12_0" to="-roadc12" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(1)_roadc12_1" to="roadc11(1)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(1)_roadc12(1)_0" to="roadc12(1)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(1)_roadc12(1)_1" to="-roadc11(1)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(2)_roadc12(1)_0" to="roadc11(2)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(2)_roadc12(1)_1" to="-roadc12(1)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(2)_roadc12(2)_0" to="roadc12(2)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(2)_roadc12(2)_1" to="-roadc11(2)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(3)_roadc12(2)_0" to="-roadc12(2)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(3)_roadc12(2)_1" to="roadc11(3)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(3)_roadc12(3)_0" to="-roadc11(3)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(3)_roadc12(3)_1" to="roadc12(3)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(4)_roadc12(4)_0" to="-roadc11(4)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(4)_roadc12(4)_0" to="-roadc11(4)" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadc11(4)_roadc12(7)_0" to="-roadc12(7)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(4)_roadc12(7)_0" to="-roadc12(7)" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadc11(5)_roadc12(4)_0" to="-roadc12(4)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(5)_roadc12(4)_0" to="-roadc12(4)" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadc11(5)_roadc12(5)_0" to="-roadc11(5)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(5)_roadc12(5)_0" to="-roadc11(5)" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadc11(6)_roadc12(5)_0" to="-roadc12(5)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(6)_roadc12(5)_0" to="-roadc12(5)" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadc11(6)_roadc12(6)_0" to="-roadc11(6)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(6)_roadc12(6)_0" to="-roadc11(6)" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadc11(7)_roadc12(6)_0" to="-roadc12(6)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(7)_roadc12(6)_0" to="-roadc12(6)" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadc11(7)_roadc12(7)_0" to="-roadc11(7)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11(7)_roadc12(7)_0" to="-roadc11(7)" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadc11_roadc12_0" to="-roadc11" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11_roadc12_1" to="roadc12" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11_roadc12(3)_0" to="-roadc12(3)" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc11_roadc12(3)_1" to="roadc11" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc22_roadc21_0" to="roadc21" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc22_roadc21_0" to="roadc21" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadc22_roadc23_0" to="roadc22" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc22_roadc23_0" to="roadc22" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadc25_roadc23_0" to="roadc23" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc25_roadc23_0" to="roadc23" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadc25_roadc24_0" to="roadc25" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc25_roadc24_0" to="roadc25" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadc26_roadc24_0" to="roadc24" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc26_roadc24_0" to="roadc24" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadc26_roadc28_0" to="roadc26" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc26_roadc28_0" to="roadc26" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadc27_roadc21_0" to="roadc27" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc27_roadc21_0" to="roadc27" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadc27_roadc28_0" to="roadc28" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":junction_roadc27_roadc28_0" to="roadc28" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":junction_roadf1-b1_end_0" to="-roadf1-b1" fromLane="0" toLane="0" dir="t" state="M"/>
    <connection from=":junction_roadf1-b1_start_0" to="roadf1-b1" fromLane="0" toLane="0" dir="t" state="M"/>
    <connection from=":junction_roadf1-b2_end_0" to="-roadf1-b2" fromLane="0" toLane="1" dir="t" state="M"/>
    <connection from=":junction_roadf1-b2_start_0" to="roadf1-b2" fromLane="0" toLane="0" dir="t" state="M"/>
    <connection from=":junction_roadpi/2_end_0" to="-roadpi/2" fromLane="0" toLane="0" dir="t" state="M"/>
    <connection from=":junction_roadpi/2_start_0" to="roadpi/2" fromLane="0" toLane="0" dir="t" state="M"/>
    <connection from=":junction_roadrad2f1_end_0" to="-roadrad2f1" fromLane="0" toLane="0" dir="t" state="M"/>
    <connection from=":junction_roadrad2f1_start_0" to="roadrad2f1" fromLane="0" toLane="0" dir="t" state="M"/>

</net>
