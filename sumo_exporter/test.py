import argparse

from map import Map


parser = argparse.ArgumentParser(
    description="Visualize roads and crossroads from Webots files"
)
parser.add_argument("wbt_file", help="Path to the Webots (.wbt) file")
parser.add_argument(
    "--show", action="store_true", help="display the visualization"
)
parser.add_argument("output_dir", help="Path to the output directory")

map = Map(parser.parse_args().wbt_file, parser.parse_args().output_dir)
map.load()
map.visualize()
map.export_to_sumo()
