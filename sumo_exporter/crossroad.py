"""Road class container."""

import math
import re

from re_definitions import floatRE, intRE
from data_structures import grouper
from lxml import etree as ET

try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    import numpy as np

    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print(
        "Warning: matplotlib not available. Crossroad visualization will be disabled."
    )


class Crossroad(object):
    """Class matching with a Webots Crossroad, containing facilities to export to SUMO junctions."""

    crossroads = dict()

    def __init__(self, crossroadType):
        """Constructor: Initialize the crossroad with a unique id."""
        self.roads = []  # connected roads
        self.translation = [0.0, 0.0, 0.0]
        self.connectedRoadIDs = []
        self.shape = []
        self.crossroadType = crossroadType

    def init_from_wbt_string(self, wbtString):
        """Extract info from the wbtString matching the node."""

        name = re.findall(r'name\s*"([^"]*)"', wbtString)
        self.name = name[0] if name else ""

        translation = re.findall(
            r"translation\s*(%s\s*%s\s*%s)" % (floatRE, floatRE, floatRE), wbtString
        )
        self.translation = (
            [float(x) for x in translation[0].split()]
            if translation
            else self.translation
        )

        rotation = re.findall(
            r"rotation\s*(%s\s*%s\s*%s\s*%s)" % (floatRE, floatRE, floatRE, floatRE),
            wbtString,
        )
        self.rotation = (
            [float(x) for x in rotation[0].split()] if rotation else [0.0, 0.0, 1.0, 0]
        )

        connectedRoadIDs = re.findall(r"connectedRoadIDs\s*\[([^\]]*)\]", wbtString)
        if connectedRoadIDs:
            self.connectedRoadIDs = [
                x.replace('"', "") for x in connectedRoadIDs[0].split()
            ]

        if self.crossroadType == "Crossroad":
            try:
                self.shape = grouper(
                    3,
                    [
                        float(x)
                        for x in re.findall(r"shape\s*\[([^\]]*)\]", wbtString)[
                            0
                        ].split()
                    ],
                )
                correction_angle = -math.pi * 0.5
                for i in range(len(self.shape)):
                    shape = self.shape[i]
                    x = (
                        -math.cos(correction_angle) * shape[0]
                        + math.sin(correction_angle) * shape[1]
                    )
                    y = (
                        math.cos(correction_angle) * shape[1]
                        + math.sin(correction_angle) * shape[0]
                    )
                    z = shape[2]
                    self.shape[i] = [x, y, z]
            except Exception:
                pass
        elif self.crossroadType == "RoadIntersection":
            self.shape = []

            roadNumber = re.findall(r"roadNumber\s*(%s)" % intRE, wbtString)
            roadNumber = int(roadNumber[0]) if roadNumber else 4

            roadsWidth = re.findall(r"roadsWidth\s*(%s)" % floatRE, wbtString)
            roadsWidth = float(roadsWidth[0]) if roadsWidth else 7.0

            outerRadius = roadsWidth / (2 * math.sin(math.pi / roadNumber))
            angle = -self.rotation[3]
            if self.rotation[2] > 0:
                angle = -angle
            for i in range(roadNumber):
                x1 = outerRadius * math.cos(2 * math.pi * i / roadNumber)
                y1 = outerRadius * math.sin(2 * math.pi * i / roadNumber)
                x2 = math.cos(angle) * x1 - math.sin(angle) * y1
                y2 = math.cos(angle) * y1 + math.sin(angle) * x1
                self.shape.append([x2, y2, 0])

    def create_node(self, nodes):
        """Populate the SUMO XML node."""
        node = ET.SubElement(nodes, "node")
        node.attrib["id"] = self.id
        node.attrib["x"] = str(self.translation[0])
        node.attrib["y"] = str(self.translation[1])
        if self.shape:
            shape = ""
            for wayPoint in self.shape:
                shape += "%f,%f " % (
                    wayPoint[0] + self.translation[0],
                    wayPoint[1] + self.translation[1],
                )
            shape += "%f,%f" % (
                self.shape[0][0] + self.translation[0],
                self.shape[0][1] + self.translation[1],
            )
            node.attrib["shape"] = shape
    
    @classmethod
    def print(cls):
        print("Crossroads Num:", len(cls.crossroads))
        for crossroad in cls.crossroads.values():
            print("name:",
                  crossroad.name,
                  "connectedRoadIDs:",
                  crossroad.connectedRoadIDs,
                  "shape:",
                  crossroad.shape)

    def visualize(
        self,
        show=True,
        save_path=None,
        ax=None,
        color="red",
        alpha=0.7,
        show_name=True,
        show_connected_roads=True,
        name_fontsize=10,
        connected_roads_fontsize=6,
    ):
        """
        可视化Crossroad对象，显示十字路口的形状、名称和连接的道路ID

        Args:
            show (bool): 是否显示图像
            save_path (str): 保存图像的路径，如果为None则不保存
            ax (matplotlib.axes.Axes): 现有的坐标轴对象，如果为None则创建新的
            color (str): 十字路口颜色
            alpha (float): 透明度
            show_name (bool): 是否显示十字路口名称
            show_connected_roads (bool): 是否显示连接的道路ID
            name_fontsize (int): 名称字体大小
            connected_roads_fontsize (int): 连接道路ID字体大小

        Returns:
            matplotlib.axes.Axes: 绘图坐标轴对象
        """
        if not MATPLOTLIB_AVAILABLE:
            print("Error: matplotlib not available. Cannot visualize crossroad.")
            return None

        # 创建新的图形或使用现有的坐标轴
        if ax is None:
            fig, ax = plt.subplots(figsize=(12, 8))
            new_figure = True
        else:
            new_figure = False

        # 获取十字路口的中心位置
        center_x = self.translation[0]
        center_y = self.translation[1]

        # 绘制十字路口形状
        if self.shape and len(self.shape) > 0:
            # 如果有形状定义，绘制多边形
            shape_coords = []
            for point in self.shape:
                shape_coords.append([point[0] + center_x, point[1] + center_y])

            if len(shape_coords) >= 3:
                # 绘制填充的多边形
                polygon = patches.Polygon(
                    shape_coords,
                    closed=True,
                    facecolor=color,
                    alpha=alpha,
                    edgecolor="darkred",
                    linewidth=2,
                    zorder=4,
                )
                ax.add_patch(polygon)
        else:
            # 如果没有形状定义，绘制一个默认的圆形表示十字路口
            default_radius = 5.0  # 默认半径
            circle = patches.Circle(
                (center_x, center_y),
                default_radius,
                facecolor=color,
                alpha=alpha,
                edgecolor="darkred",
                linewidth=2,
                zorder=4,
            )
            ax.add_patch(circle)

        # 绘制中心点
        ax.scatter(center_x, center_y, color="darkred", s=50, zorder=6, alpha=0.9)

        # 显示十字路口名称（在中央）
        if show_name:
            display_name = self.name
            ax.text(
                center_x,
                center_y + 2,
                display_name,
                ha="center",
                va="bottom",
                fontsize=name_fontsize,
                fontweight="bold",
                color="black",
                bbox=dict(
                    boxstyle="round,pad=0.3",
                    facecolor="white",
                    alpha=0.8,
                    edgecolor="darkred",
                ),
                zorder=7,
            )

        # 显示连接的道路ID和十字路口ID
        if show_connected_roads:
            if self.connectedRoadIDs:
                connected_text = (
                    f"Crossroad ID: {self.name}\nConnected Roads:\n"
                    + "\n".join(self.connectedRoadIDs)
                )
            else:
                connected_text = f"Crossroad ID: {self.name}\nConnected Roads: None"
            ax.text(
                center_x,
                center_y - 3,
                connected_text,
                ha="center",
                va="top",
                fontsize=connected_roads_fontsize,
                color="darkblue",
                bbox=dict(
                    boxstyle="round,pad=0.3",
                    facecolor="lightyellow",
                    alpha=0.8,
                    edgecolor="darkblue",
                ),
                zorder=7,
            )

        # 设置图形属性（仅在创建新图形时）
        if new_figure:
            ax.set_title(f"Crossroad Visualization: {self.name or self.id}")
            ax.set_xlabel("X Coordinate (m)")
            ax.set_ylabel("Y Coordinate (m)")
            ax.grid(True, alpha=0.3)
            ax.axis("equal")

            # 保存图像
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches="tight")

            # 显示图像
            if show:
                plt.show()
            else:
                plt.close()

        return ax

    @classmethod
    def visualize_all_crossroads(
        cls,
        crossroads=None,
        show=True,
        save_path=None,
        figsize=(15, 10),
        show_names=True,
        show_connected_roads=True,
        color_by_type=True,
    ):
        """
        可视化所有Crossroad对象

        Args:
            crossroads (list): Crossroad对象列表，如果为None则使用cls.crossroads
            show (bool): 是否显示图像
            save_path (str): 保存图像的路径
            figsize (tuple): 图像大小
            show_names (bool): 是否显示十字路口名称
            show_connected_roads (bool): 是否显示连接的道路ID
            color_by_type (bool): 是否根据十字路口类型使用不同颜色

        Returns:
            matplotlib.axes.Axes: 绘图坐标轴对象
        """
        if not MATPLOTLIB_AVAILABLE:
            print("Error: matplotlib not available. Cannot visualize crossroads.")
            return None

        if crossroads is None:
            crossroads = cls.crossroads

        if not crossroads:
            print("No crossroads to visualize.")
            return None

        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)

        # 定义不同十字路口类型的颜色
        type_colors = {
            "Crossroad": "red",
            "RoadIntersection": "orange",
            "LaneSeparation": "purple",
        }

        # 绘制所有十字路口
        for i, crossroad in enumerate(crossroads):
            # 选择颜色
            if color_by_type and crossroad.crossroadType in type_colors:
                color = type_colors[crossroad.crossroadType]
            else:
                # 使用循环颜色
                colors = [
                    "red",
                    "orange",
                    "purple",
                    "brown",
                    "pink",
                    "gray",
                    "olive",
                    "cyan",
                ]
                color = colors[i % len(colors)]

            # 绘制十字路口
            crossroad.visualize(
                show=False,
                ax=ax,
                color=color,
                show_name=show_names,
                show_connected_roads=show_connected_roads,
            )

        # 设置图形属性
        ax.set_title("All Crossroads Visualization")
        ax.set_xlabel("X Coordinate (m)")
        ax.set_ylabel("Y Coordinate (m)")
        ax.grid(True, alpha=0.3)
        ax.axis("equal")

        # 添加统计信息
        stats_text = f"Total Crossroads: {len(crossroads)}\n"
        type_counts = {}
        for crossroad in crossroads:
            type_counts[crossroad.crossroadType] = (
                type_counts.get(crossroad.crossroadType, 0) + 1
            )

        for crossroad_type, count in type_counts.items():
            stats_text += f"{crossroad_type}: {count}\n"

        ax.text(
            0.02,
            0.98,
            stats_text,
            transform=ax.transAxes,
            verticalalignment="top",
            bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
        )

        # 保存图像
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")

        # 显示图像
        if show:
            plt.show()
        else:
            plt.close()

        return ax
